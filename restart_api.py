#!/usr/bin/env python3
"""
快速重启FastAPI服务的脚本
"""
import os
import sys
import time
import subprocess
import signal
import psutil

def find_fastapi_process():
    """查找FastAPI进程"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('uvicorn' in cmd or 'main:app' in cmd for cmd in cmdline):
                return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def kill_fastapi_process():
    """终止FastAPI进程"""
    pid = find_fastapi_process()
    if pid:
        try:
            print(f"🔄 正在终止FastAPI进程 (PID: {pid})")
            os.kill(pid, signal.SIGTERM)
            time.sleep(2)
            
            # 检查进程是否还在运行
            if find_fastapi_process():
                print("⚠️ 进程仍在运行，强制终止...")
                os.kill(pid, signal.SIGKILL)
                time.sleep(1)
            
            print("✅ FastAPI进程已终止")
            return True
        except Exception as e:
            print(f"❌ 终止进程失败: {e}")
            return False
    else:
        print("ℹ️ 未找到运行中的FastAPI进程")
        return True

def start_fastapi():
    """启动FastAPI服务"""
    print("🚀 正在启动FastAPI服务...")
    
    # 切换到项目目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 启动服务
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "backend.app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ]
        
        print(f"📝 执行命令: {' '.join(cmd)}")
        
        # 在新的控制台窗口中启动
        if os.name == 'nt':  # Windows
            subprocess.Popen(
                cmd,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
        else:  # Linux/Mac
            subprocess.Popen(cmd)
        
        print("✅ FastAPI服务启动命令已执行")
        print("⏳ 等待服务启动...")
        time.sleep(3)
        
        return True
    except Exception as e:
        print(f"❌ 启动服务失败: {e}")
        return False

def check_service():
    """检查服务状态"""
    import requests
    
    print("🔍 检查服务状态...")
    
    for i in range(10):  # 最多等待10秒
        try:
            response = requests.get("http://localhost:8000/api/v1/status", timeout=2)
            if response.status_code == 200:
                print("✅ 服务启动成功！")
                print(f"📊 状态: {response.json()}")
                return True
        except:
            pass
        
        print(f"⏳ 等待服务启动... ({i+1}/10)")
        time.sleep(1)
    
    print("❌ 服务启动超时")
    return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔄 FastAPI服务重启工具")
    print("=" * 50)
    
    # 1. 终止现有进程
    if not kill_fastapi_process():
        print("❌ 无法终止现有进程，请手动终止后重试")
        return False
    
    # 2. 启动新服务
    if not start_fastapi():
        print("❌ 启动服务失败")
        return False
    
    # 3. 检查服务状态
    if check_service():
        print("\n🎉 服务重启成功！")
        print("🌐 现在可以测试CORS跨域访问了")
        print("📝 测试地址: http://127.0.0.1:5500/test_cors.html")
        return True
    else:
        print("\n❌ 服务重启失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 重启完成")
        else:
            print("\n❌ 重启失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)
