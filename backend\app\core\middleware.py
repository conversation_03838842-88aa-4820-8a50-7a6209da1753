"""
中间件配置
"""
import time
import sys
import os
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from backend.config.logging_config import get_logger

logger = get_logger(__name__)


def add_middlewares(app: FastAPI) -> None:
    """添加中间件"""

    # CORS中间件 - 更宽松的配置以确保跨域访问
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 开发环境允许所有来源
        allow_credentials=False,  # 当allow_origins为*时，必须设为False
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["*"],
    )
    
    # 请求日志中间件
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        """请求日志中间件"""
        start_time = time.time()
        
        response = await call_next(request)
        
        process_time = time.time() - start_time
        logger.info(
            f"{request.method} {request.url.path} - "
            f"Status: {response.status_code} - "
            f"Time: {process_time:.3f}s"
        )
        
        return response
