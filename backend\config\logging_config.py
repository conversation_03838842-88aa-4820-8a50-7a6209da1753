"""
统一日志配置模块
解决多处日志配置冲突问题
"""
import logging
import logging.config
import sys
from pathlib import Path
from typing import Dict, Any


def setup_logging(log_level: str = "INFO", log_file: str = None) -> None:
    """
    设置统一的日志配置
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径，如果为None则只输出到控制台
    """
    
    # 转换日志级别
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 配置字典
    config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'standard': {
                'format': log_format,
                'datefmt': date_format
            },
            'detailed': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
                'datefmt': date_format
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'level': log_level.upper(),
                'formatter': 'standard',
                'stream': sys.stdout
            }
        },
        'loggers': {
            # 应用根 logger
            '': {
                'level': log_level.upper(),
                'handlers': ['console'],
                'propagate': False
            },
            # 应用特定 logger
            'backend': {
                'level': log_level.upper(),
                'handlers': ['console'],
                'propagate': False
            },
            # uvicorn logger
            'uvicorn': {
                'level': 'INFO',
                'handlers': ['console'],
                'propagate': False
            },
            'uvicorn.access': {
                'level': 'INFO',
                'handlers': ['console'],
                'propagate': False
            },
            'uvicorn.error': {
                'level': 'INFO',
                'handlers': ['console'],
                'propagate': False
            },
            # FastAPI logger
            'fastapi': {
                'level': log_level.upper(),
                'handlers': ['console'],
                'propagate': False
            },
            # 第三方库日志级别控制
            'qdrant_client': {
                'level': 'WARNING',
                'handlers': ['console'],
                'propagate': False
            },
            'httpx': {
                'level': 'WARNING',
                'handlers': ['console'],
                'propagate': False
            },
            'openai': {
                'level': 'WARNING',
                'handlers': ['console'],
                'propagate': False
            },
            'llama_index': {
                'level': 'WARNING',
                'handlers': ['console'],
                'propagate': False
            }
        }
    }
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        config['handlers']['file'] = {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': log_level.upper(),
            'formatter': 'detailed',
            'filename': log_file,
            'maxBytes': 10 * 1024 * 1024,  # 10MB
            'backupCount': 5,
            'encoding': 'utf-8'
        }
        
        # 为所有 logger 添加文件处理器
        for logger_name in config['loggers']:
            if logger_name not in ['uvicorn', 'uvicorn.access', 'uvicorn.error']:
                config['loggers'][logger_name]['handlers'].append('file')
    
    # 应用配置
    logging.config.dictConfig(config)
    
    # 设置根 logger 级别
    logging.getLogger().setLevel(numeric_level)
    
    print(f"✅ 日志系统配置完成 - 级别: {log_level.upper()}")


def get_logger(name: str) -> logging.Logger:
    """
    获取配置好的 logger
    
    Args:
        name: logger 名称
        
    Returns:
        logging.Logger: 配置好的 logger 实例
    """
    return logging.getLogger(name)


def test_logging():
    """测试各个日志级别是否正常工作"""
    logger = get_logger(__name__)
    
    print("\n🧪 测试日志系统...")
    print("=" * 50)
    
    logger.debug("这是一条 DEBUG 消息")
    logger.info("这是一条 INFO 消息")
    logger.warning("这是一条 WARNING 消息")
    logger.error("这是一条 ERROR 消息")
    logger.critical("这是一条 CRITICAL 消息")
    
    print("=" * 50)
    print("✅ 日志测试完成")


if __name__ == "__main__":
    # 测试日志配置
    setup_logging("DEBUG")
    test_logging()
