#!/usr/bin/env python3
"""
简单的查询测试脚本
"""

import requests
import json

def test_query():
    """测试查询功能"""
    url = "http://localhost:8000/api/v1/query"
    data = {
        "query": "什么是人工智能？",
        "max_results": 3
    }
    
    try:
        print("🔍 测试查询功能...")
        print(f"URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
        
        response = requests.post(url, json=data, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 查询成功!")
            print(f"答案长度: {len(result.get('answer', ''))}")
            print(f"源文档数: {len(result.get('sources', []))}")
            print(f"处理时间: {result.get('processing_time', 0):.2f}秒")
            
            # 显示答案的前200个字符
            answer = result.get('answer', '')
            if answer:
                print(f"答案预览: {answer[:200]}...")
            
            return True
        else:
            print(f"❌ 查询失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_status():
    """测试状态接口"""
    url = "http://localhost:8000/api/v1/status"
    
    try:
        print("📊 测试状态接口...")
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 状态接口正常!")
            print(f"文档数量: {result.get('documents_count')}")
            print(f"存储大小: {result.get('storage_size')}")
            print(f"集合名称: {result.get('collection_name')}")
            return True
        else:
            print(f"❌ 状态接口失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 状态接口异常: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Qdrant迁移功能测试")
    print("=" * 40)
    
    # 测试状态
    status_ok = test_status()
    print()
    
    # 测试查询
    if status_ok:
        query_ok = test_query()
        
        if query_ok:
            print("\n🎉 所有测试通过！Qdrant迁移成功！")
        else:
            print("\n⚠️ 查询测试失败")
    else:
        print("\n❌ 状态测试失败，跳过查询测试")
