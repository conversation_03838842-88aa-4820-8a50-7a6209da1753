#!/usr/bin/env python3
"""
测试API日志输出
通过调用API来触发各种级别的日志
"""
import requests
import time
import json


def test_api_logging():
    """测试API调用产生的日志"""
    base_url = "http://localhost:8000"
    
    print("🧪 测试API日志输出")
    print("=" * 60)
    
    # 1. 测试正常API调用（INFO级别日志）
    print("\n📡 1. 测试状态API（正常情况）")
    try:
        response = requests.get(f"{base_url}/api/v1/status")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    time.sleep(1)
    
    # 2. 测试不存在的API（可能产生WARNING/ERROR日志）
    print("\n📡 2. 测试不存在的API（错误情况）")
    try:
        response = requests.get(f"{base_url}/api/v1/nonexistent")
        print(f"   状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    time.sleep(1)
    
    # 3. 测试查询API（可能产生各种日志）
    print("\n📡 3. 测试查询API")
    try:
        query_data = {
            "question": "测试问题",
            "vector_weight": 0.5,
            "bm25_weight": 0.5
        }
        response = requests.post(
            f"{base_url}/api/v1/query",
            json=query_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   查询成功: {result.get('success', False)}")
        else:
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    time.sleep(1)
    
    # 4. 测试错误的查询参数（可能产生ERROR日志）
    print("\n📡 4. 测试错误的查询参数")
    try:
        bad_query_data = {
            "question": "",  # 空问题
            "vector_weight": 1.5,  # 错误的权重
            "bm25_weight": -0.5   # 错误的权重
        }
        response = requests.post(
            f"{base_url}/api/v1/query",
            json=bad_query_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    print("\n✅ API日志测试完成")
    print("📋 请查看服务器终端输出，应该能看到各种级别的日志")


if __name__ == "__main__":
    test_api_logging()
