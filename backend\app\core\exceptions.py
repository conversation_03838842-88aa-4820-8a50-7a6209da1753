"""
自定义异常和异常处理器
"""
import sys
import os
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from backend.config.settings import settings
from backend.config.logging_config import get_logger

logger = get_logger(__name__)


class RAGServiceError(Exception):
    """RAG服务异常"""
    pass


class DocumentNotFoundError(Exception):
    """文档未找到异常"""
    pass


def add_exception_handlers(app: FastAPI) -> None:
    """添加异常处理器"""
    
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """全局异常处理器"""
        logger.error(f"未处理的异常: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": True,
                "message": "服务器内部错误",
                "detail": str(exc) if settings.debug else "请联系管理员"
            }
        )
    
    @app.exception_handler(RAGServiceError)
    async def rag_service_exception_handler(request: Request, exc: RAGServiceError):
        """RAG服务异常处理器"""
        logger.error(f"RAG服务异常: {exc}")
        return JSONResponse(
            status_code=503,
            content={
                "error": True,
                "message": "RAG服务异常",
                "detail": str(exc)
            }
        )
    
    @app.exception_handler(DocumentNotFoundError)
    async def document_not_found_exception_handler(request: Request, exc: DocumentNotFoundError):
        """文档未找到异常处理器"""
        logger.warning(f"文档未找到: {exc}")
        return JSONResponse(
            status_code=404,
            content={
                "error": True,
                "message": "文档未找到",
                "detail": str(exc)
            }
        )
