# RAG聊天机器人与CMS数据同步功能 PRD

## 1. 背景

### 1.1 现状
- 已实现RAG聊天机器人的文档增删改查功能
- 已实现services/mysql_service.py的MySQL连接测试
- 现有RAG系统可以处理用户上传的文档并进行问答

### 1.2 问题
- RAG系统与chestnut_cms数据库内容分离，无法利用CMS中的丰富文章内容
- 用户问答时无法追溯到原始文章来源
- 缺乏CMS数据的自动同步机制

## 2. 目标

### 2.1 主要目标
在documents页面添加同步功能，实现RAG聊天机器人与chestnut_cms数据库的数据同步，使用户能够基于CMS文章内容进行问答，并可追溯到原始文章。

### 2.2 具体目标
1. 实现CMS文章数据的一键同步到RAG系统
2. 支持文章的增删改查同步
3. 在问答回复中显示文章来源信息
4. 提供文章链接跳转功能

## 3. 功能需求

### 3.1 数据同步功能

#### 3.1.1 同步触发
- **位置**: documents.html页面
- **触发方式**: 手动点击同步按钮
- **同步类型**: 一次性全量同步
- **进度显示**: 显示进度条和当前处理文章的标题

#### 3.1.2 数据源配置
- **CMS数据库**: chestnut_cms (配置信息存储在项目根目录.env文件中)
- **目标存储**: ChromaDB + 本地文件系统
- **连接管理**: 复用现有MySQL连接配置

### 3.2 数据处理规则

#### 3.2.1 数据筛选条件
- **处理范围**: cms_content表中link_flag为NULL的记录
- **排除条件**: link_flag为"Y"的外链文章(redirect_url不为空)

#### 3.2.2 数据获取逻辑
从以下表获取数据：
- **cms_content表**: content_id, catalog_id, title, publish_date, link_flag
- **cms_catalog表**: 通过catalog_id获取path字段
- **cms_article_detail表**: 通过content_id获取content_html字段

#### 3.2.3 文件URL生成规则
```
file_url = "https://www.gzmdrw.cn/{path}{content_id}.shtml"
```
其中path来自cms_catalog表的path字段

### 3.3 同步操作类型

#### 3.3.1 新增操作
**触发条件**: content_id存在于cms_content但不存在于ChromaDB

**执行步骤**:
1. 从cms_article_detail获取content_html
2. 清理HTML标签，保留换行和段落结构
3. 生成{content_id}.txt文件存储到data目录
4. 构建file_url
5. 将文档添加到RAG系统
6. 在ChromaDB metadata中存储: content_id, title, file_url, publish_date

#### 3.3.2 删除操作
**触发条件**: content_id存在于ChromaDB但不存在于cms_content

**执行步骤**:
1. 删除data目录中的{content_id}.txt文件
2. 删除ChromaDB中的向量数据
3. 删除相关元数据

#### 3.3.3 更新操作
**触发条件**: content_id在两个系统中都存在，但publish_date不一致

**执行步骤**:
1. 删除旧的向量数据
2. 删除旧的{content_id}.txt文件
3. 删除旧的元数据
4. 创建新的{content_id}.txt文件
5. 重新向量化并存储新数据

### 3.4 HTML内容处理

#### 3.4.1 清理规则
- 移除所有HTML标签
- 保留段落结构: `<p>` → `\n\n`
- 保留换行: `<br>` → `\n`
- 保留基本分段: `<div>` → `\n`
- 移除多媒体内容: 图片、表格、视频、音频、PDF、DOC等附件

#### 3.4.2 特殊字符处理
- **保留字符**: 【】'""'！《》()（）及各种emoji
- **处理原则**: 只要不影响数据存储和提取，特殊字符可以保留
- **编码**: 统一使用UTF-8编码

### 3.5 ChromaDB元数据扩展

#### 3.5.1 新增元数据字段
```json
{
    "content_id": "文章ID",
    "title": "文章标题", 
    "file_url": "文章链接",
    "publish_date": "发布日期",
    "file_path": "文件路径",
    "file_name": "文件名"
}
```

#### 3.5.2 现有数据处理
- 清空现有的6个文档及其向量数据
- 重新开始，确保数据结构一致性

### 3.6 问答功能增强

#### 3.6.1 回答格式调整
- **原格式**: 仅显示答案和相关文档片段
- **新格式**: 在每个文档片段前显示对应文章标题
- **标题功能**: 点击标题跳转到对应的file_url

#### 3.6.2 多文章处理
- 如果回答涉及多篇文章，显示所有相关文章的标题
- 每个文档片段独立显示其来源文章标题

## 4. 技术实现要点

### 4.1 数据库查询
```sql
SELECT 
    c.content_id, c.catalog_id, c.title, c.publish_date,
    cat.path,
    ad.content_html
FROM cms_content c
LEFT JOIN cms_catalog cat ON c.catalog_id = cat.catalog_id  
LEFT JOIN cms_article_detail ad ON c.content_id = ad.content_id
WHERE c.link_flag IS NULL
```

### 4.2 性能考虑
- **同步时间**: 首次同步最多30分钟，后续同步较快
- **处理方式**: 全量对比，不分批处理
- **错误处理**: 同步失败时显示错误信息，用户手动重试

### 4.3 文件存储
- **存储位置**: data目录
- **命名规则**: {content_id}.txt
- **唯一性**: content_id保证文件名唯一性

## 5. 测试验证

### 5.1 功能测试
1. **同步按钮测试**: 验证按钮点击触发同步流程
2. **进度显示测试**: 验证进度条和文章标题显示
3. **数据获取测试**: 验证从三个表正确获取数据
4. **HTML处理测试**: 验证HTML标签清理效果
5. **文件生成测试**: 验证txt文件正确生成
6. **向量化测试**: 验证文档成功添加到RAG系统

### 5.2 同步逻辑测试
1. **新增测试**: 在CMS中添加新文章，验证同步后RAG系统包含该文章
2. **删除测试**: 在CMS中删除文章，验证同步后RAG系统移除该文章
3. **更新测试**: 修改CMS文章发布日期，验证同步后内容更新
4. **筛选测试**: 验证link_flag为"Y"的文章不被同步

### 5.3 问答功能测试
1. **标题显示测试**: 验证回答中正确显示文章标题
2. **链接跳转测试**: 验证点击标题正确跳转到原文
3. **多文章测试**: 验证涉及多篇文章时的显示效果
4. **特殊字符测试**: 验证包含特殊字符的标题正常显示

### 5.4 边界情况测试
1. **空内容测试**: content_html为空时的处理
2. **网络异常测试**: 数据库连接失败时的错误处理
3. **文件权限测试**: data目录无写权限时的处理
4. **大文件测试**: 超大文章内容的处理性能

## 6. 验收标准

### 6.1 功能完整性
- [ ] 同步按钮正常工作
- [ ] 进度显示准确
- [ ] 数据正确同步到RAG系统
- [ ] 问答回复包含文章来源信息
- [ ] 链接跳转功能正常

### 6.2 数据准确性
- [ ] 同步的文章数量与CMS中符合条件的文章数量一致
- [ ] 文章内容正确去除HTML标签
- [ ] 元数据信息完整准确
- [ ] 文件URL格式正确

### 6.3 用户体验
- [ ] 同步过程有明确的进度反馈
- [ ] 错误信息清晰易懂
- [ ] 问答界面美观实用
- [ ] 响应速度满足要求

## 7. 风险与限制

### 7.1 技术风险
- 首次同步时间较长，可能影响用户体验
- HTML内容复杂时清理效果可能不理想
- 大量数据同步时可能出现内存压力

### 7.2 业务限制
- 仅处理link_flag为NULL的文章
- 不支持自动定时同步
- 同步失败时需要手动重试

### 7.3 数据风险
- 更新操作采用删除重建策略，中途失败可能导致数据丢失
- 依赖用户手动重新同步恢复数据
