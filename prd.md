# BM25与向量混合检索系统改造PRD

## 1. 项目概述

### 1.1 项目背景
当前RAG系统使用单纯的向量检索（基于text-embedding-3-small模型），虽然在语义相似度匹配方面表现良好，但在精确关键词匹配和术语检索方面存在不足。为了提升检索准确性和覆盖面，需要引入BM25（Best Matching 25）算法与向量检索相结合的混合检索方案。

### 1.2 技术栈现状
- **后端框架**: FastAPI
- **RAG框架**: LlamaIndex
- **向量数据库**: Qdrant (localhost:6333)
- **文档存储**: ChromaDB + Qdrant混合架构
- **嵌入模型**: text-embedding-3-small (1536维)
- **LLM模型**: GPT-4o-mini

### 1.3 项目目标
1. 实现BM25与向量检索的混合检索机制
2. 提供灵活的权重配置API，支持用户自定义检索策略
3. 保持现有API兼容性，确保平滑升级
4. 提升检索准确性和用户体验

## 2. 需求分析

### 2.1 功能需求

#### 2.1.1 混合检索核心功能
- **BM25检索**: 基于关键词匹配的稀疏检索
- **向量检索**: 基于语义相似度的密集检索
- **结果融合**: 使用LlamaIndex的QueryFusionRetriever进行结果合并和重排序

#### 2.1.2 权重配置需求
- **默认权重**: BM25权重0.5，向量检索权重0.5
- **权重约束**: 
  - 每个权重值范围: [0.0, 1.0]
  - 两个权重值之和必须等于1.0
  - BM25权重为0时，仅执行向量检索
  - 向量权重为0时，仅执行BM25检索

#### 2.1.3 API接口需求
- 扩展现有`/api/v1/query`接口，增加权重参数
- 保持向后兼容性，未指定权重时使用默认值
- 提供权重验证和错误处理机制

### 2.2 性能需求
- **响应时间**: 混合检索响应时间不超过当前向量检索的1.5倍
- **准确性**: 混合检索的相关性评分应优于单一检索方法
- **并发性**: 支持当前系统的并发查询能力

### 2.3 兼容性需求
- 保持现有数据结构不变
- 现有客户端代码无需修改即可继续使用
- 支持渐进式迁移策略

## 3. 技术方案设计

### 3.1 架构设计

#### 3.1.1 检索器架构
```
QueryRequest → RAGService → QueryFusionRetriever
                              ├── VectorStoreRetriever (Qdrant)
                              └── BM25Retriever (LlamaIndex)
                                    ↓
                              Result Fusion & Reranking
                                    ↓
                              QueryResponse
```

#### 3.1.2 权重控制机制
- 使用LlamaIndex的`QueryFusionRetriever`的`retriever_weights`参数
- 实现权重验证逻辑确保合规性
- 支持动态权重调整

### 3.2 实现方案

#### 3.2.1 数据模型扩展
**扩展QueryRequest模型**:
- 添加`bm25_weight: float = 0.5`字段
- 添加`vector_weight: float = 0.5`字段
- 实现权重验证器

**扩展QueryResponse模型**:
- 添加`retrieval_mode: str`字段，标识使用的检索模式
- 添加`weights_used: dict`字段，记录实际使用的权重

#### 3.2.2 RAGService改造
**核心改造点**:
1. 重构`_create_query_engine`方法，支持混合检索
2. 实现BM25Retriever初始化逻辑
3. 配置QueryFusionRetriever with权重参数
4. 添加检索模式判断逻辑

**关键实现步骤**:
1. 初始化BM25Retriever: `BM25Retriever.from_defaults(docstore=index.docstore)`
2. 创建向量检索器: `index.as_retriever(similarity_top_k=k)`
3. 配置QueryFusionRetriever: 设置`retriever_weights`、`mode="relative_score"`
4. 实现权重为0时的单一检索逻辑

#### 3.2.3 API接口改造
**查询接口扩展**:
- 路径: `POST /api/v1/query`
- 新增可选参数: `bm25_weight`, `vector_weight`
- 实现参数验证中间件
- 保持现有参数完全兼容

## 4. 详细实施步骤

### 4.1 第一阶段：基础架构准备 (1-2天)

#### 4.1.1 依赖检查和安装
- 验证LlamaIndex版本支持QueryFusionRetriever和BM25Retriever
- 检查llama-index-retrievers-bm25包是否已安装
- 确认Qdrant客户端版本兼容性

#### 4.1.2 数据模型扩展
- 修改`backend/app/models/requests.py`中的QueryRequest
- 添加权重字段和验证逻辑
- 修改`backend/app/models/responses.py`中的QueryResponse
- 添加检索模式标识字段

#### 4.1.3 配置文件更新
- 在`backend/config/settings.py`中添加混合检索相关配置
- 设置默认权重值、检索器参数等
- 添加BM25相关配置选项

### 4.2 第二阶段：核心检索逻辑实现 (2-3天)

#### 4.2.1 RAGService核心改造
- 重构`_create_query_engine`方法
- 实现BM25Retriever初始化
- 配置QueryFusionRetriever
- 添加权重验证和检索模式判断

#### 4.2.2 检索器集成
- 集成向量检索器和BM25检索器
- 实现结果融合逻辑
- 配置相对评分模式（relative_score）
- 处理边界情况（权重为0的情况）

#### 4.2.3 错误处理机制
- 实现权重验证逻辑
- 添加检索失败回退机制
- 完善日志记录和监控

### 4.3 第三阶段：API接口集成 (1天)

#### 4.3.1 查询接口改造
- 修改`backend/app/api/v1/query.py`
- 添加权重参数处理逻辑
- 实现参数验证中间件
- 保持向后兼容性

#### 4.3.2 响应格式扩展
- 在响应中添加检索模式信息
- 记录实际使用的权重值
- 提供检索性能指标

### 4.4 第四阶段：测试和优化 (2-3天)

#### 4.4.1 单元测试
- 编写权重验证测试用例
- 测试各种权重组合的检索效果
- 验证边界情况处理

#### 4.4.2 集成测试
- 测试API接口完整性
- 验证向后兼容性
- 性能基准测试

#### 4.4.3 性能调优
- 优化检索器参数配置
- 调整融合算法参数
- 监控内存和CPU使用情况

### 4.5 第五阶段：文档和部署 (1天)

#### 4.5.1 文档更新
- 更新API文档，说明新增参数
- 编写混合检索使用指南
- 提供最佳实践建议

#### 4.5.2 部署准备
- 准备数据库迁移脚本（如需要）
- 配置生产环境参数
- 制定回滚方案

## 5. 风险评估与应对

### 5.1 技术风险
**风险**: BM25检索器初始化可能需要重建文档索引
**应对**: 实现增量索引更新机制，避免全量重建

**风险**: 混合检索性能可能不如预期
**应对**: 实现性能监控，提供检索模式降级机制

### 5.2 兼容性风险
**风险**: 现有客户端可能受到影响
**应对**: 严格保持API向后兼容，新参数设为可选

### 5.3 数据风险
**风险**: 检索结果质量可能下降
**应对**: 实现A/B测试机制，支持快速回滚

## 6. 验收标准

### 6.1 功能验收
- [ ] 支持BM25和向量检索的混合模式
- [ ] 权重参数验证正确执行
- [ ] 边界情况（权重为0）正确处理
- [ ] API向后兼容性完全保持

### 6.2 性能验收
- [ ] 混合检索响应时间 ≤ 原向量检索的1.5倍
- [ ] 系统并发能力不下降
- [ ] 内存使用增长 ≤ 20%

### 6.3 质量验收
- [ ] 单元测试覆盖率 ≥ 90%
- [ ] 集成测试通过率 100%
- [ ] 文档完整性和准确性验证

## 7. 后续优化方向

### 7.1 短期优化
- 实现检索结果缓存机制
- 添加检索性能监控面板
- 支持更多融合算法选择

### 7.2 长期规划
- 引入学习排序（Learning to Rank）算法
- 实现用户个性化检索权重
- 支持多模态检索扩展
