#!/usr/bin/env python3
"""
测试同步比较逻辑
检查CMS和RAG中的文章日期比较
"""
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.services.chestnut_cms_service import chestnut_cms_service

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_sync_comparison():
    """测试同步比较逻辑"""
    print("=== 测试同步比较逻辑 ===")
    
    try:
        # 获取CMS文章
        print("\n1. 获取CMS文章...")
        cms_articles = chestnut_cms_service.mysql_service.get_chestnut_cms_articles()
        print(f"CMS文章数量: {len(cms_articles)}")
        
        # 显示前3篇CMS文章的信息
        for i, article in enumerate(cms_articles[:3]):
            print(f"  CMS文章 {i+1}: ID={article['content_id']}, 标题='{article['title']}', 日期='{article['publish_date']}' (类型: {type(article['publish_date'])})")
        
        # 获取RAG文章
        print("\n2. 获取RAG文章...")
        rag_articles = chestnut_cms_service.get_rag_articles_info()
        print(f"RAG文章数量: {len(rag_articles)}")
        
        # 显示前3篇RAG文章的信息
        for i, (content_id, article) in enumerate(list(rag_articles.items())[:3]):
            print(f"  RAG文章 {i+1}: ID={content_id}, 标题='{article.get('title', '无标题')}', 日期='{article['publish_date']}' (类型: {type(article['publish_date'])})")
        
        # 进行比较
        print("\n3. 进行比较...")
        operations = chestnut_cms_service.compare_articles(cms_articles, rag_articles)
        
        print(f"\n比较结果:")
        print(f"  新增: {len(operations['to_add'])}")
        print(f"  更新: {len(operations['to_update'])}")
        print(f"  删除: {len(operations['to_delete'])}")
        print(f"  保持: {len(operations['to_keep'])}")
        
        # 显示需要更新的文章详情
        if operations['to_update']:
            print(f"\n需要更新的文章:")
            for article in operations['to_update'][:5]:  # 只显示前5个
                content_id = str(article['content_id'])
                cms_date = str(article["publish_date"]) if article["publish_date"] else None
                rag_date = rag_articles.get(content_id, {}).get("publish_date")
                print(f"  ID={content_id}, 标题='{article['title']}', CMS日期='{cms_date}', RAG日期='{rag_date}'")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sync_comparison()
