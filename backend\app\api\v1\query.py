"""
查询相关API路由
"""
import time
import logging
from fastapi import APIRouter, Depends, HTTPException

from backend.app.dependencies import get_rag_service
from backend.app.models.requests import QueryRequest
from backend.app.models.responses import QueryResponse
from backend.app.services.rag_service import RAGService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/query", response_model=QueryResponse)
async def query_documents(
    request: QueryRequest,
    rag_service: RAGService = Depends(get_rag_service)
):
    """查询问答"""
    try:
        start_time = time.time()
        result = rag_service.query(
            question=request.query,
            max_results=request.max_results,
            bm25_weight=request.bm25_weight,
            vector_weight=request.vector_weight
        )
        processing_time = time.time() - start_time

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])

        response_data = {
            "answer": result["answer"],
            "sources": result["sources"],
            "processing_time": processing_time,
            "total_sources": result["total_sources"],
            "retrieval_mode": result.get("retrieval_mode", "vector_only"),
            "weights_used": result.get("weights_used")
        }

        return QueryResponse(**response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
