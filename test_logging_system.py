#!/usr/bin/env python3
"""
测试日志系统是否正常工作
验证各个日志级别和不同模块的日志输出
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.config.logging_config import setup_logging, get_logger


def test_basic_logging():
    """测试基本日志功能"""
    print("\n🧪 测试基本日志功能")
    print("=" * 60)
    
    # 设置 DEBUG 级别以查看所有日志
    setup_logging("DEBUG")
    
    # 获取不同模块的 logger
    main_logger = get_logger("backend.app.main")
    rag_logger = get_logger("backend.app.services.rag_service")
    cms_logger = get_logger("backend.app.services.chestnut_cms_service")
    
    print("\n📝 测试不同日志级别:")
    main_logger.debug("🔍 这是一条 DEBUG 消息")
    main_logger.info("ℹ️ 这是一条 INFO 消息")
    main_logger.warning("⚠️ 这是一条 WARNING 消息")
    main_logger.error("❌ 这是一条 ERROR 消息")
    main_logger.critical("🚨 这是一条 CRITICAL 消息")
    
    print("\n📝 测试不同模块的日志:")
    rag_logger.info("📚 RAG服务日志测试")
    cms_logger.info("🔄 CMS服务日志测试")
    
    print("\n✅ 基本日志测试完成")


def test_logging_levels():
    """测试不同日志级别的过滤效果"""
    print("\n🧪 测试日志级别过滤")
    print("=" * 60)
    
    levels = ["DEBUG", "INFO", "WARNING", "ERROR"]
    
    for level in levels:
        print(f"\n📊 设置日志级别为: {level}")
        setup_logging(level)
        
        logger = get_logger(f"test.{level.lower()}")
        
        print(f"   测试各级别日志输出:")
        logger.debug(f"   🔍 DEBUG - 当前级别: {level}")
        logger.info(f"   ℹ️ INFO - 当前级别: {level}")
        logger.warning(f"   ⚠️ WARNING - 当前级别: {level}")
        logger.error(f"   ❌ ERROR - 当前级别: {level}")
    
    print("\n✅ 日志级别测试完成")


def test_application_loggers():
    """测试应用中实际使用的 logger"""
    print("\n🧪 测试应用 Logger")
    print("=" * 60)
    
    setup_logging("DEBUG")
    
    # 模拟应用中的各种日志场景
    app_loggers = [
        ("backend.app.main", "主应用"),
        ("backend.app.services.rag_service", "RAG服务"),
        ("backend.app.services.chestnut_cms_service", "CMS服务"),
        ("backend.app.services.mysql_service", "MySQL服务"),
        ("backend.app.api.v1.chat", "聊天API"),
        ("backend.app.api.v1.documents", "文档API"),
        ("backend.app.core.middleware", "中间件"),
        ("backend.app.core.exceptions", "异常处理"),
    ]
    
    for logger_name, description in app_loggers:
        logger = get_logger(logger_name)
        logger.info(f"✅ {description} - 日志系统正常")
        logger.debug(f"🔍 {description} - DEBUG级别日志")
        logger.warning(f"⚠️ {description} - 警告级别日志")
    
    print("\n✅ 应用Logger测试完成")


def test_exception_logging():
    """测试异常日志记录"""
    print("\n🧪 测试异常日志记录")
    print("=" * 60)
    
    setup_logging("DEBUG")
    logger = get_logger("test.exception")
    
    try:
        # 故意触发异常
        result = 1 / 0
    except Exception as e:
        logger.error(f"❌ 捕获到异常: {e}", exc_info=True)
        logger.exception("🚨 使用 exception 方法记录异常")
    
    print("\n✅ 异常日志测试完成")


def main():
    """主测试函数"""
    print("🔍 日志系统全面测试")
    print("=" * 80)
    
    try:
        test_basic_logging()
        test_logging_levels()
        test_application_loggers()
        test_exception_logging()
        
        print("\n" + "=" * 80)
        print("🎉 所有日志测试完成！")
        print("📋 如果您看到了各种级别的日志消息，说明日志系统工作正常")
        print("📋 如果某些级别的日志没有显示，说明日志级别过滤正常工作")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
