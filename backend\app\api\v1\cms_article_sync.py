"""
CMS文章同步相关API路由
"""
import time
import logging
from fastapi import APIRouter, HTTPException

from backend.app.services.chestnut_cms_service import chestnut_cms_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/chestnut-cms")
async def sync_chestnut_cms():
    """同步ChestnutCMS文章到RAG系统"""
    try:
        start_time = time.time()
        result = chestnut_cms_service.sync_articles()
        processing_time = time.time() - start_time
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        # 更新处理时间
        result["processing_time"] = processing_time
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步失败: {str(e)}")


@router.get("/status")
async def get_sync_status():
    """获取同步状态"""
    try:
        status = chestnut_cms_service.get_sync_status()
        return status
        
    except Exception as e:
        logger.error(f"获取同步状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取同步状态失败: {str(e)}")


@router.get("/test-connection")
async def test_chestnut_cms_connection():
    """测试ChestnutCMS连接"""
    try:
        result = chestnut_cms_service.test_connection()
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"连接测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"连接测试失败: {str(e)}")