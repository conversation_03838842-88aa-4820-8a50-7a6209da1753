# 日志系统修复总结

## 问题描述

项目的日志系统出现了系统级别的问题，只显示 INFO 级别的日志，其他级别（DEBUG、WARNING、ERROR、CRITICAL）的日志都没有显示。

## 问题原因分析

1. **多处日志配置冲突**：
   - `main.py` 中有 `logging.basicConfig()` 配置
   - 多个脚本文件也有自己的 `logging.basicConfig()` 配置
   - `logging.basicConfig()` 只在第一次调用时生效，后续调用被忽略

2. **uvicorn 覆盖应用日志配置**：
   - uvicorn 启动时会重新配置日志系统
   - uvicorn 的日志配置覆盖了应用的日志配置
   - 应用的 logger 没有正确继承根 logger 的配置

3. **日志级别设置不统一**：
   - 不同模块使用不同的日志配置方式
   - 缺乏统一的日志管理机制

## 解决方案

### 1. 创建统一日志配置模块

创建了 `backend/config/logging_config.py`，提供：

- **统一的日志配置函数** `setup_logging()`
- **标准化的 logger 获取函数** `get_logger()`
- **支持不同日志级别**：DEBUG、INFO、WARNING、ERROR、CRITICAL
- **支持文件日志**（可选）
- **第三方库日志级别控制**

### 2. 更新所有模块使用统一配置

更新了以下文件使用新的日志配置：

- `backend/app/main.py` - 主应用
- `backend/app/services/rag_service.py` - RAG服务
- `backend/app/services/chestnut_cms_service.py` - CMS服务
- `backend/app/services/mysql_service.py` - MySQL服务
- `backend/app/core/middleware.py` - 中间件
- `backend/app/core/exceptions.py` - 异常处理
- `backend/app/core/events.py` - 事件处理
- `backend/app/dependencies.py` - 依赖注入

### 3. 配置 uvicorn 使用自定义日志

修改了 `main.py` 中的 uvicorn 配置：

- 使用自定义的 `log_config` 字典
- 禁用 uvicorn 的访问日志 (`access_log=False`)
- 避免颜色代码干扰 (`use_colors=False`)

## 修复效果

### ✅ 修复前的问题
- 只显示 INFO 级别日志
- 缺少 DEBUG、WARNING、ERROR 日志
- 日志配置混乱，多处冲突

### ✅ 修复后的效果
- **所有日志级别正常显示**：DEBUG、INFO、WARNING、ERROR、CRITICAL
- **模块化日志**：每个模块都有正确的名称前缀
- **统一日志格式**：`时间戳 - 模块名 - 级别 - 消息`
- **可配置日志级别**：支持 DEBUG/INFO/WARNING/ERROR 级别切换
- **第三方库日志控制**：避免第三方库日志干扰

## 测试验证

创建了多个测试脚本验证日志系统：

1. **`test_logging_system.py`** - 全面测试日志系统功能
2. **`test_manual_logging.py`** - 手动测试各模块日志
3. **`test_api_logging.py`** - 测试API调用产生的日志

## 配置说明

### 日志级别设置

在 `main.py` 中：
```python
# 根据环境设置日志级别
log_level = "DEBUG" if settings.debug else "INFO"
setup_logging(log_level)
```

### 获取 Logger

在任何模块中：
```python
from backend.config.logging_config import get_logger
logger = get_logger(__name__)
```

### 使用示例

```python
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")
logger.exception("异常信息")  # 自动包含堆栈跟踪
```

## 最佳实践

1. **统一使用 `get_logger(__name__)`** 获取 logger
2. **避免直接使用 `logging.basicConfig()`**
3. **在应用启动时调用 `setup_logging()`**
4. **根据环境设置合适的日志级别**
5. **使用 `logger.exception()` 记录异常信息**

## 文件清单

### 新增文件
- `backend/config/logging_config.py` - 统一日志配置模块
- `test_logging_system.py` - 日志系统测试脚本
- `test_manual_logging.py` - 手动日志测试脚本
- `test_api_logging.py` - API日志测试脚本
- `docs/日志系统修复总结.md` - 本文档

### 修改文件
- `backend/app/main.py` - 使用统一日志配置
- `backend/app/services/rag_service.py` - 更新日志配置
- `backend/app/services/chestnut_cms_service.py` - 更新日志配置
- `backend/app/services/mysql_service.py` - 更新日志配置
- `backend/app/core/middleware.py` - 更新日志配置
- `backend/app/core/exceptions.py` - 更新日志配置
- `backend/app/core/events.py` - 更新日志配置
- `backend/app/dependencies.py` - 更新日志配置

## 总结

通过创建统一的日志配置模块和更新所有相关文件，成功解决了项目日志系统的问题。现在系统能够正确显示所有级别的日志，并且具有良好的可维护性和可扩展性。

日志系统现在支持：
- ✅ 所有日志级别（DEBUG、INFO、WARNING、ERROR、CRITICAL）
- ✅ 模块化日志管理
- ✅ 统一的日志格式
- ✅ 可配置的日志级别
- ✅ 第三方库日志控制
- ✅ 异常堆栈跟踪
- ✅ 可选的文件日志输出
