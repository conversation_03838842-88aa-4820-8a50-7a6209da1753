/**
 * AI聊天应用JavaScript
 * 使用原生JavaScript实现，不依赖Vue.js
 */

// 配置
const CONFIG = {
  API_BASE_URL: "http://localhost:8000/api/v1",
  MAX_RESULTS: 3,
  SIMILARITY_THRESHOLD: 0.7,
};

// 应用状态
const state = {
  messages: [],
  isLoading: false,
  connectionStatus: {
    text: "正在检查连接...",
    class: "text-gray-500",
  },
};

// DOM元素
const elements = {
  chatMessages: null,
  userInput: null,
  sendBtn: null,
  clearBtn: null,
  connectionStatus: null,
  loadingMessage: null,
  topicBtns: null,
};

// 初始化应用
function initApp() {
  console.log("初始化AI聊天应用...");

  // 获取DOM元素
  elements.chatMessages = document.getElementById("chat-messages");
  elements.userInput = document.getElementById("user-input");
  elements.sendBtn = document.getElementById("send-btn");
  elements.clearBtn = document.getElementById("clear-btn");
  elements.connectionStatus = document.getElementById("connection-status");
  elements.loadingMessage = document.getElementById("loading-message");
  elements.topicBtns = document.querySelectorAll(".topic-btn");
  // 移动端菜单元素通过全局函数直接访问，无需存储

  // 绑定事件
  bindEvents();

  // 初始化消息
  initMessages();

  // 检查连接状态
  checkConnection();

  console.log("应用初始化完成");
}

// 绑定事件
function bindEvents() {
  // 发送按钮
  elements.sendBtn.addEventListener("click", sendMessage);

  // 清空按钮
  elements.clearBtn.addEventListener("click", clearMessages);

  // 输入框回车事件
  elements.userInput.addEventListener("keydown", (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  });

  // 快捷话题按钮
  elements.topicBtns.forEach((btn) => {
    btn.addEventListener("click", (e) => {
      const topic = e.target.getAttribute("data-topic");
      if (topic) {
        elements.userInput.value = topic;
        sendMessage();
      }
    });
  });

  // 移动端菜单 - 使用HTML onclick，无需额外绑定
}

// 初始化消息
function initMessages() {
  state.messages = [
    {
      type: "ai",
      content:
        '你好!我是贵阳人文科技学院的智能助手"文文"，致力于为师生提供便捷的信息服务。',
      timestamp: Date.now(),
    },
  ];

  // 渲染初始消息（欢迎消息已在HTML中）
}

// 检查连接状态
async function checkConnection() {
  console.log("检查API连接状态...");

  try {
    // 使用AbortController实现超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(`${CONFIG.API_BASE_URL}/status`, {
      method: "GET",
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      updateConnectionStatus("✅ 服务连接正常", "text-green-600");
      console.log("连接成功:", data);
    } else {
      throw new Error(`HTTP ${response.status}`);
    }
  } catch (error) {
    console.error("连接检查失败:", error);
    if (error.name === "AbortError") {
      updateConnectionStatus("❌ 连接超时，请检查服务是否启动", "text-red-600");
    } else {
      updateConnectionStatus(
        "❌ 服务连接失败，请检查服务是否启动",
        "text-red-600"
      );
    }
  }
}

// 更新连接状态
function updateConnectionStatus(text, className) {
  state.connectionStatus.text = text;
  state.connectionStatus.class = className;

  if (elements.connectionStatus) {
    elements.connectionStatus.textContent = text;
    elements.connectionStatus.className = `text-sm ${className}`;
  }
}

// 发送消息
async function sendMessage() {
  const message = elements.userInput.value.trim();
  if (!message || state.isLoading) {
    return;
  }

  console.log("发送消息:", message);

  // 添加用户消息
  addMessage("user", message);

  // 清空输入框
  elements.userInput.value = "";

  // 显示加载状态
  showLoading(true);

  try {
    // 使用AbortController实现超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

    const response = await fetch(`${CONFIG.API_BASE_URL}/query`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        query: message,
        max_results: CONFIG.MAX_RESULTS,
        similarity_threshold: CONFIG.SIMILARITY_THRESHOLD,
      }),
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log("收到回复:", data);

    if (data.answer) {
      // 添加AI回复
      addMessage("ai", data.answer, data.sources);
    } else {
      addMessage("ai", "抱歉，我没有找到相关信息。");
    }
  } catch (error) {
    console.error("发送消息失败:", error);
    let errorMessage = "抱歉，发生了错误：";

    if (error.name === "AbortError") {
      errorMessage += "请求超时，请稍后重试。";
    } else if (error.message.includes("HTTP")) {
      errorMessage += `服务器错误 ${error.message}`;
    } else if (error.name === "TypeError") {
      errorMessage += "无法连接到服务器，请检查服务是否启动。";
    } else {
      errorMessage += error.message;
    }

    addMessage("ai", errorMessage, null, true);
  } finally {
    showLoading(false);
  }
}

// 添加消息到界面
function addMessage(type, content, sources = null, isError = false) {
  const message = {
    type,
    content,
    sources,
    isError,
    timestamp: Date.now(),
  };

  state.messages.push(message);
  renderMessage(message);
  scrollToBottom();
}

// 渲染单条消息
function renderMessage(message) {
  const messageDiv = document.createElement("div");
  messageDiv.className = "flex items-start gap-3";

  if (message.type === "user") {
    messageDiv.classList.add("self-end");
  }

  // 头像
  const avatarDiv = document.createElement("div");
  avatarDiv.className =
    "w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0";

  if (message.type === "ai") {
    avatarDiv.className += " border-4 border-[#ad1e23]";
    avatarDiv.innerHTML =
      '<img src="images/ww.png" alt="" class="w-full h-full rounded-full object-cover" onerror="this.style.display=\'none\'" />';
  } else {
    avatarDiv.className += " bg-gray-300";
    avatarDiv.innerHTML =
      '<img src="images/men.jpg" alt="" class="w-full h-full rounded-full object-cover" onerror="this.style.display=\'none\'" />';
  }

  // 消息内容
  const contentDiv = document.createElement("div");
  const bgClass =
    message.type === "ai"
      ? "bg-primary-100 rounded-2xl rounded-tl-none"
      : "bg-gray-100 rounded-2xl rounded-tr-none";
  contentDiv.className = `${bgClass} p-4 w-[80%] break-words chat-message`;

  // 文本内容
  const textDiv = document.createElement("div");
  if (message.isError) {
    textDiv.className = "text-red-600";
  }

  if (message.type === "ai") {
    textDiv.innerHTML = formatMessage(message.content);
  } else {
    textDiv.textContent = message.content;
  }

  contentDiv.appendChild(textDiv);

  // 添加源信息
  if (message.sources && message.sources.length > 0) {
    const sourcesDiv = createSourcesDiv(message.sources);
    contentDiv.appendChild(sourcesDiv);
  }

  // 组装消息
  if (message.type === "ai") {
    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);
  } else {
    messageDiv.appendChild(contentDiv);
    messageDiv.appendChild(avatarDiv);
  }

  elements.chatMessages.appendChild(messageDiv);
}

// 创建源信息显示
function createSourcesDiv(sources) {
  const sourcesDiv = document.createElement("div");
  sourcesDiv.className = "mt-3 space-y-2 text-sm";

  const titleDiv = document.createElement("h4");
  titleDiv.className = "font-semibold text-primary-700";
  titleDiv.textContent = "参考来源：";
  sourcesDiv.appendChild(titleDiv);

  const listDiv = document.createElement("ul");
  listDiv.className = "source-list";

  sources.forEach((source, index) => {
    const sourceItem = document.createElement("li");
    sourceItem.className = "source-item";

    const numberSpan = document.createElement("span");
    numberSpan.className = "source-number";
    numberSpan.textContent = `[${index + 1}]`;

    const titleSpan = document.createElement("span");
    titleSpan.className = "source-title";

    // 处理源标题和链接
    const title = getSourceTitle(source);
    const url = getSourceUrl(source);

    if (url) {
      const link = document.createElement("a");
      link.href = url;
      link.target = "_blank";
      link.rel = "noopener noreferrer";
      link.textContent = title;
      titleSpan.appendChild(link);
    } else {
      titleSpan.textContent = title;
    }

    // 创建预览框
    const previewDiv = document.createElement("div");
    previewDiv.className = "source-preview";
    // 使用完整的content内容，如果没有则使用content_preview，最后才使用默认文本
    previewDiv.textContent =
      source.content || source.content_preview || "暂无内容预览";

    sourceItem.appendChild(numberSpan);
    sourceItem.appendChild(titleSpan);
    sourceItem.appendChild(previewDiv);
    listDiv.appendChild(sourceItem);
  });

  sourcesDiv.appendChild(listDiv);
  return sourcesDiv;
}

// 获取源标题
function getSourceTitle(source) {
  if (source.metadata && source.metadata.title) {
    // 解码Unicode编码
    let title = source.metadata.title;
    if (title.includes("\\u")) {
      title = title.replace(/\\u[\dA-Fa-f]{4}/g, function (match) {
        return String.fromCharCode(parseInt(match.replace(/\\u/g, ""), 16));
      });
    }
    return title;
  }
  return source.metadata?.filename || source.filename || "未知文件";
}

// 获取源URL
function getSourceUrl(source) {
  return source.metadata?.file_url || null;
}

// 格式化消息内容
function formatMessage(content) {
  if (!content) return "";

  // 简单的Markdown格式化
  return content
    .replace(/\n/g, "<br>")
    .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
    .replace(/\*(.*?)\*/g, "<em>$1</em>");
}

// 显示/隐藏加载状态
function showLoading(show) {
  state.isLoading = show;

  if (elements.loadingMessage) {
    elements.loadingMessage.style.display = show ? "flex" : "none";
  }

  if (elements.sendBtn) {
    elements.sendBtn.disabled = show;
  }
}

// 清空消息
function clearMessages() {
  if (confirm("确定要清空所有对话历史吗？此操作不可撤销。")) {
    console.log("清空对话历史");

    // 清空状态
    state.messages = [];

    // 清空界面，保留欢迎消息
    const welcomeMessage = elements.chatMessages.querySelector(
      ".flex.items-start.gap-3"
    );
    elements.chatMessages.innerHTML = "";
    if (welcomeMessage) {
      elements.chatMessages.appendChild(welcomeMessage);
    }

    // 重新添加欢迎消息
    addMessage(
      "ai",
      '你好!我是贵阳人文科技学院的智能助手"文文"，致力于为师生提供便捷的信息服务。'
    );
  }
}

// 滚动到底部
function scrollToBottom() {
  if (elements.chatMessages) {
    elements.chatMessages.scrollTop = elements.chatMessages.scrollHeight;
  }
}

// 切换移动端菜单 - 全局函数
function toggleMobileMenu() {
  console.log("toggleMobileMenu 被调用");
  const mobileNav = document.getElementById("mobile-nav");
  console.log("找到菜单元素:", mobileNav);

  if (mobileNav) {
    const isHidden = mobileNav.classList.contains("hidden");
    console.log("当前状态 - 隐藏:", isHidden);

    if (isHidden) {
      mobileNav.classList.remove("hidden");
      console.log("显示菜单");
    } else {
      mobileNav.classList.add("hidden");
      console.log("隐藏菜单");
    }
  } else {
    console.error("未找到移动端菜单元素");
  }
}

// 页面加载完成后初始化
document.addEventListener("DOMContentLoaded", initApp);

// 确保toggleMobileMenu函数在全局作用域中可用
window.toggleMobileMenu = toggleMobileMenu;
