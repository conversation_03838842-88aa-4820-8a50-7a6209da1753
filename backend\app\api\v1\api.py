"""
API v1路由聚合
"""
from fastapi import APIRouter

from .query import router as query_router
from .documents import router as documents_router
from .health import router as health_router
from .cms_article_sync import router as cms_sync_router

api_router = APIRouter()

# 包含各个路由模块
api_router.include_router(query_router, tags=["查询"])
api_router.include_router(documents_router, prefix="/documents", tags=["文档管理"])
api_router.include_router(health_router, tags=["健康检查"])
api_router.include_router(cms_sync_router, prefix="/sync", tags=["CMS文章同步"])
