/**
 * 文档管理页面样式
 */

/* 页面容器 */
.documents-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e5e7eb;
}

.page-title {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 10px;
}

.nav-links {
  display: flex;
  gap: 15px;
}

.nav-link {
  padding: 8px 16px;
  background: #6366f1;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background: #4f46e5;
}

/* 上传区域 */
.upload-section {
  background: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  margin-bottom: 30px;
  transition: border-color 0.2s;
}

.upload-section:hover {
  border-color: #6366f1;
}

.upload-section.dragover {
  border-color: #4f46e5;
  background: #eef2ff;
}

.upload-icon {
  font-size: 3rem;
  color: #6b7280;
  margin-bottom: 15px;
}

.upload-text {
  font-size: 1.1rem;
  color: #374151;
  margin-bottom: 20px;
}

.file-input-wrapper {
  position: relative;
  display: inline-block;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.upload-btn {
  background: #6366f1;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.upload-btn:hover {
  background: #4f46e5;
}

/* 数据同步区域 */
.sync-section {
  background: #f0f9ff;
  border: 2px solid #0ea5e9;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
}

.sync-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.sync-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #0c4a6e;
  display: flex;
  align-items: center;
  gap: 10px;
}

.sync-btn {
  background: #0ea5e9;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sync-btn:hover:not(:disabled) {
  background: #0284c7;
}

.sync-btn:disabled {
  background: #94a3b8;
  cursor: not-allowed;
}

.sync-description {
  margin-bottom: 15px;
}

.sync-description p {
  margin: 0 0 5px 0;
  color: #374151;
  font-size: 0.95rem;
}

.sync-description small {
  color: #6b7280;
  font-size: 0.85rem;
}

.sync-progress {
  display: none;
  margin-top: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: #374151;
}

.progress-bar-container {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #0ea5e9, #06b6d4);
  border-radius: 4px;
  transition: width 0.3s ease;
  width: 0%;
}

.current-item {
  font-size: 0.9rem;
  color: #6b7280;
  font-style: italic;
}

/* 文档列表 */
.documents-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  background: #f3f4f6;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.refresh-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refresh-btn:hover {
  background: #059669;
}

.documents-table {
  width: 100%;
  border-collapse: collapse;
}

.documents-table th,
.documents-table td {
  padding: 15px 20px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.documents-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.documents-table tr:hover {
  background: #f9fafb;
}

.file-name {
  font-weight: 500;
  color: #1f2937;
}

.file-stats {
  color: #6b7280;
  font-size: 0.9rem;
}

.delete-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.delete-btn:hover {
  background: #dc2626;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.loading i {
  font-size: 2rem;
  margin-bottom: 10px;
}

/* 提示信息 */
.alert {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: none;
}

.alert.success {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.alert.error {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

.alert.info {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

/* 统计栏 */
.stats-bar {
  background: #f3f4f6;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #6b7280;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .documents-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .sync-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .stats-bar {
    flex-direction: column;
    gap: 10px;
  }

  .documents-table {
    font-size: 0.9rem;
  }

  .documents-table th,
  .documents-table td {
    padding: 10px;
  }
}
