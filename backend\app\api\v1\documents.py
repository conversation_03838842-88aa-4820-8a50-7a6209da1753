"""
文档管理相关API路由
"""
import time
import logging
from typing import List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File

from backend.app.dependencies import get_rag_service
from backend.app.models.responses import (
    LoadDocumentsResponse, 
    UploadDocumentResponse, 
    DeleteDocumentResponse
)
from backend.app.models.domain import DocumentsListResponse
from backend.app.services.rag_service import RAGService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/load", response_model=LoadDocumentsResponse)
async def load_documents(rag_service: RAGService = Depends(get_rag_service)):
    """加载data目录中的所有文档"""
    try:
        start_time = time.time()
        result = rag_service.load_documents()
        processing_time = time.time() - start_time
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        response_data = {
            "success": result["success"],
            "message": result["message"],
            "documents_processed": result["documents_processed"],
            "replaced_files": result.get("replaced_files", []),
            "new_files": result.get("new_files", []),
            "processing_time": processing_time
        }
        
        return LoadDocumentsResponse(**response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"加载文档失败: {e}")
        raise HTTPException(status_code=500, detail=f"加载文档失败: {str(e)}")


@router.get("/list", response_model=DocumentsListResponse)
async def list_documents(rag_service: RAGService = Depends(get_rag_service)):
    """获取文档列表"""
    try:
        result = rag_service.get_documents_list()
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return DocumentsListResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")


@router.post("/upload", response_model=UploadDocumentResponse)
async def upload_document(
    file: UploadFile = File(...),
    rag_service: RAGService = Depends(get_rag_service)
):
    """上传单个文档"""
    try:
        # 检查文件类型
        if not file.filename.endswith('.txt'):
            raise HTTPException(status_code=400, detail="只支持.txt文件")
        
        # 读取文件内容
        content = await file.read()
        file_content = content.decode('utf-8')
        
        # 上传文档
        result = rag_service.upload_document(file_content, file.filename)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return UploadDocumentResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文档失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传文档失败: {str(e)}")


@router.delete("/{filename}", response_model=DeleteDocumentResponse)
async def delete_document(
    filename: str,
    rag_service: RAGService = Depends(get_rag_service)
):
    """删除指定文档"""
    try:
        result = rag_service.delete_document(filename)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return DeleteDocumentResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文档失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除文档失败: {str(e)}")
