#!/usr/bin/env python3
"""
清理旧的ChromaDB/SQLite存储文件和目录
在迁移到Qdrant后，这些文件不再需要
"""
import os
import shutil
from pathlib import Path

def cleanup_storage_directories():
    """清理storage目录"""
    print("🧹 清理旧的storage目录...")
    
    # 要清理的目录列表
    storage_dirs = [
        "./storage",
        "./backend/storage"
    ]
    
    for storage_dir in storage_dirs:
        storage_path = Path(storage_dir)
        if storage_path.exists():
            print(f"📁 发现目录: {storage_path}")
            
            # 列出目录内容
            files = list(storage_path.rglob("*"))
            if files:
                print(f"   包含 {len(files)} 个文件/目录:")
                for file_path in files:
                    if file_path.is_file():
                        size_mb = file_path.stat().st_size / (1024 * 1024)
                        print(f"   - {file_path.relative_to(storage_path)}: {size_mb:.2f}MB")
                    else:
                        print(f"   - {file_path.relative_to(storage_path)}/")
            
            # 询问是否删除
            response = input(f"是否删除 {storage_path}? (y/N): ").strip().lower()
            if response == 'y':
                try:
                    shutil.rmtree(storage_path)
                    print(f"✅ 已删除: {storage_path}")
                except Exception as e:
                    print(f"❌ 删除失败: {e}")
            else:
                print(f"⏭️  跳过: {storage_path}")
        else:
            print(f"📁 目录不存在: {storage_path}")

def cleanup_old_scripts():
    """列出可能需要清理的旧脚本"""
    print("\n🔍 检查可能需要清理的旧ChromaDB脚本...")
    
    old_scripts = [
        "scripts/check_chromadb_storage.py",
        "scripts/diagnose_hnsw_error.py", 
        "scripts/rebuild_database.py",
        "scripts/clean_and_test_fix.py",
        "scripts/fast_reset_database.py"
    ]
    
    existing_scripts = []
    for script in old_scripts:
        script_path = Path(script)
        if script_path.exists():
            existing_scripts.append(script_path)
            print(f"   📄 {script}")
    
    if existing_scripts:
        print(f"\n发现 {len(existing_scripts)} 个旧的ChromaDB脚本")
        print("这些脚本现在可能不再需要，因为系统已迁移到Qdrant")
        print("你可以手动检查并决定是否保留它们")
    else:
        print("   ✅ 没有发现旧脚本")

def cleanup_env_files():
    """检查环境配置文件中的旧配置"""
    print("\n🔍 检查环境配置文件...")
    
    env_files = [".env", ".env.local", ".env.example", ".env.backup"]
    
    for env_file in env_files:
        env_path = Path(env_file)
        if env_path.exists():
            print(f"   📄 {env_file}")
            try:
                with open(env_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 检查是否包含旧的配置
                old_configs = [
                    "STORAGE_DIR",
                    "CHROMA_PERSIST_DIRECTORY"
                ]
                
                found_old_configs = []
                for config in old_configs:
                    if config in content:
                        found_old_configs.append(config)
                
                if found_old_configs:
                    print(f"      ⚠️  包含旧配置: {', '.join(found_old_configs)}")
                else:
                    print(f"      ✅ 配置已更新")
                    
            except Exception as e:
                print(f"      ❌ 读取失败: {e}")

def main():
    """主函数"""
    print("🚀 开始清理旧的ChromaDB/SQLite存储文件")
    print("=" * 60)
    
    print("⚠️  注意: 这将删除旧的ChromaDB数据文件")
    print("   如果你还需要这些数据，请先备份")
    print("   现在系统使用Qdrant作为向量数据库")
    
    response = input("\n继续清理? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ 取消清理")
        return
    
    # 执行清理步骤
    cleanup_storage_directories()
    cleanup_old_scripts()
    cleanup_env_files()
    
    print("\n🎉 清理完成!")
    print("现在系统完全使用Qdrant作为向量数据库")
    print("数据存储在Qdrant服务器中，不再依赖本地SQLite文件")

if __name__ == "__main__":
    main()
