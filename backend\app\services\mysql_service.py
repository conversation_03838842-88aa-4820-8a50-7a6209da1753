"""
MySQL 数据服务
用于连接和操作 chestnut_cms 数据库
"""
import logging
import pymysql
from typing import Optional, Dict, Any, List
from contextlib import contextmanager
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from backend.config.settings import settings
from backend.config.logging_config import get_logger

logger = get_logger(__name__)


class MySQLService:
    """MySQL 数据库服务类"""
    
    def __init__(self):
        self.connection_config = {
            'host': settings.mysql_host,
            'port': settings.mysql_port,
            'user': settings.mysql_user,
            'password': settings.mysql_password,
            'database': settings.mysql_database,
            'charset': 'utf8mb4',
            'autocommit': True,
            'connect_timeout': 10,
            'read_timeout': 30,
            'write_timeout': 30
        }
        logger.info(f"MySQL服务初始化完成 - 目标数据库: {settings.mysql_host}:{settings.mysql_port}/{settings.mysql_database}")
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试数据库连接
        
        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            logger.info("开始测试MySQL连接...")
            
            # 尝试建立连接
            connection = pymysql.connect(**self.connection_config)
            
            with connection.cursor() as cursor:
                # 测试基本查询
                cursor.execute("SELECT VERSION() as version")
                version_result = cursor.fetchone()
                
                # 测试数据库访问
                cursor.execute("SELECT DATABASE() as current_db")
                db_result = cursor.fetchone()
                
                # 获取表列表
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                # 获取连接信息
                cursor.execute("SELECT CONNECTION_ID() as connection_id")
                connection_id = cursor.fetchone()
            
            connection.close()
            
            result = {
                "success": True,
                "message": "MySQL连接测试成功",
                "connection_info": {
                    "host": settings.mysql_host,
                    "port": settings.mysql_port,
                    "database": settings.mysql_database,
                    "user": settings.mysql_user,
                    "mysql_version": version_result[0] if version_result else "未知",
                    "current_database": db_result[0] if db_result else "未知",
                    "connection_id": connection_id[0] if connection_id else "未知",
                    "tables_count": len(tables),
                    "tables": [table[0] for table in tables] if tables else []
                }
            }
            
            logger.info(f"MySQL连接测试成功 - 版本: {result['connection_info']['mysql_version']}, 表数量: {result['connection_info']['tables_count']}")
            return result
            
        except pymysql.Error as e:
            error_msg = f"MySQL连接失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "error_code": e.args[0] if e.args else None,
                "error_details": str(e),
                "connection_config": {
                    "host": settings.mysql_host,
                    "port": settings.mysql_port,
                    "database": settings.mysql_database,
                    "user": settings.mysql_user
                }
            }
        except Exception as e:
            error_msg = f"连接测试发生未知错误: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "error_details": str(e)
            }
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器
        
        Yields:
            pymysql.Connection: 数据库连接对象
        """
        connection = None
        try:
            connection = pymysql.connect(**self.connection_config)
            logger.debug("数据库连接已建立")
            yield connection
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            raise
        finally:
            if connection:
                connection.close()
                logger.debug("数据库连接已关闭")
    
    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        执行查询语句
        
        Args:
            query (str): SQL查询语句
            params (Optional[tuple]): 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute(query, params)
                    results = cursor.fetchall()
                    logger.debug(f"查询执行成功，返回 {len(results)} 条记录")
                    return results
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            raise
    
    def execute_update(self, query: str, params: Optional[tuple] = None) -> int:
        """
        执行更新语句（INSERT, UPDATE, DELETE）
        
        Args:
            query (str): SQL语句
            params (Optional[tuple]): 参数
            
        Returns:
            int: 受影响的行数
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    affected_rows = cursor.execute(query, params)
                    conn.commit()
                    logger.debug(f"更新执行成功，影响 {affected_rows} 行")
                    return affected_rows
        except Exception as e:
            logger.error(f"更新执行失败: {e}")
            raise
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """
        获取表信息
        
        Args:
            table_name (str): 表名
            
        Returns:
            Dict[str, Any]: 表信息
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    # 获取表结构
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    
                    # 获取表记录数
                    cursor.execute(f"SELECT COUNT(*) as record_count FROM {table_name}")
                    count_result = cursor.fetchone()
                    
                    return {
                        "table_name": table_name,
                        "columns": columns,
                        "record_count": count_result['record_count'] if count_result else 0
                    }
        except Exception as e:
            logger.error(f"获取表信息失败: {e}")
            raise
    
    def get_chestnut_cms_articles(self) -> List[Dict[str, Any]]:
        """
        获取ChestnutCMS文章数据
        
        Returns:
            List[Dict[str, Any]]: 文章数据列表
        """
        try:
            query = """
            SELECT 
                c.content_id, 
                c.catalog_id, 
                c.title, 
                c.publish_date, 
                c.link_flag, 
                c.redirect_url,
                cat.path,
                ad.content_html
            FROM cms_content c
            LEFT JOIN cms_catalog cat ON c.catalog_id = cat.catalog_id  
            LEFT JOIN cms_article_detail ad ON c.content_id = ad.content_id
            WHERE c.link_flag IS NULL
            ORDER BY c.publish_date DESC
            """
            
            logger.info("开始获取ChestnutCMS文章数据...")
            results = self.execute_query(query)
            logger.info(f"成功获取 {len(results)} 篇文章")
            
            return results
            
        except Exception as e:
            logger.error(f"获取ChestnutCMS文章失败: {e}")
            raise
    
    def get_chestnut_cms_article_by_id(self, content_id: int) -> Optional[Dict[str, Any]]:
        """
        根据content_id获取单篇文章
        
        Args:
            content_id (int): 文章ID
            
        Returns:
            Optional[Dict[str, Any]]: 文章数据，不存在时返回None
        """
        try:
            query = """
            SELECT 
                c.content_id, 
                c.catalog_id, 
                c.title, 
                c.publish_date, 
                c.link_flag, 
                c.redirect_url,
                cat.path,
                ad.content_html
            FROM cms_content c
            LEFT JOIN cms_catalog cat ON c.catalog_id = cat.catalog_id  
            LEFT JOIN cms_article_detail ad ON c.content_id = ad.content_id
            WHERE c.content_id = %s AND c.link_flag IS NULL
            """
            
            results = self.execute_query(query, (content_id,))
            return results[0] if results else None
            
        except Exception as e:
            logger.error(f"获取文章 {content_id} 失败: {e}")
            raise
    
    def get_chestnut_cms_articles_summary(self) -> Dict[str, Any]:
        """
        获取ChestnutCMS文章统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    # 获取总文章数
                    cursor.execute("SELECT COUNT(*) as total FROM cms_content WHERE link_flag IS NULL")
                    total_result = cursor.fetchone()
                    
                    # 获取有内容的文章数
                    cursor.execute("""
                        SELECT COUNT(*) as with_content 
                        FROM cms_content c 
                        LEFT JOIN cms_article_detail ad ON c.content_id = ad.content_id
                        WHERE c.link_flag IS NULL AND ad.content_html IS NOT NULL
                    """)
                    with_content_result = cursor.fetchone()
                    
                    # 获取最新文章日期
                    cursor.execute("SELECT MAX(publish_date) as latest_date FROM cms_content WHERE link_flag IS NULL")
                    latest_date_result = cursor.fetchone()
                    
                    # 获取最老文章日期
                    cursor.execute("SELECT MIN(publish_date) as oldest_date FROM cms_content WHERE link_flag IS NULL")
                    oldest_date_result = cursor.fetchone()
                    
                    return {
                        "total_articles": total_result['total'] if total_result else 0,
                        "articles_with_content": with_content_result['with_content'] if with_content_result else 0,
                        "latest_date": str(latest_date_result['latest_date']) if latest_date_result and latest_date_result['latest_date'] else None,
                        "oldest_date": str(oldest_date_result['oldest_date']) if oldest_date_result and oldest_date_result['oldest_date'] else None
                    }
                    
        except Exception as e:
            logger.error(f"获取文章统计失败: {e}")
            raise


# 全局实例
mysql_service = MySQLService()


def get_mysql_service() -> MySQLService:
    """
    获取MySQL服务实例（用于依赖注入）
    
    Returns:
        MySQLService: MySQL服务实例
    """
    return mysql_service


if __name__ == "__main__":
    """
    直接运行此文件进行连接测试
    """
    import json
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🔍 开始 MySQL 连接测试...")
    print("=" * 50)
    
    # 创建服务实例
    service = MySQLService()
    
    # 执行连接测试
    result = service.test_connection()
    
    # 输出结果
    print("\n📋 测试结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    if result["success"]:
        print("\n✅ 连接测试成功！")
        
        # 如果连接成功，显示更多信息
        conn_info = result["connection_info"]
        print(f"\n📊 数据库信息:")
        print(f"  - 服务器: {conn_info['host']}:{conn_info['port']}")
        print(f"  - 数据库: {conn_info['current_database']}")
        print(f"  - MySQL版本: {conn_info['mysql_version']}")
        print(f"  - 连接ID: {conn_info['connection_id']}")
        print(f"  - 表数量: {conn_info['tables_count']}")
        
        if conn_info['tables']:
            print(f"\n📄 数据库表列表:")
            for i, table in enumerate(conn_info['tables'], 1):
                print(f"  {i}. {table}")
    else:
        print("\n❌ 连接测试失败！")
        print(f"错误信息: {result['message']}")