"""
响应模型定义
"""
from typing import List, Dict, Optional
from pydantic import BaseModel, Field


class QueryResponse(BaseModel):
    """查询响应模型"""
    answer: str = Field(..., description="AI生成的回答")
    sources: list = Field(..., description="相关文档片段")
    processing_time: float = Field(..., description="处理时间（秒）")
    total_sources: int = Field(..., description="源文档数量")

    # 混合检索相关字段
    retrieval_mode: str = Field(
        default="vector_only",
        description="检索模式：hybrid(混合), vector_only(纯向量), bm25_only(纯BM25)"
    )
    weights_used: Optional[Dict[str, float]] = Field(
        default=None,
        description="实际使用的权重配置"
    )


class LoadDocumentsResponse(BaseModel):
    """加载文档响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="处理消息")
    documents_processed: int = Field(..., description="处理的文档数量")
    replaced_files: list = Field(default=[], description="被替换的文件列表")
    new_files: list = Field(default=[], description="新增的文件列表")
    processing_time: float = Field(..., description="处理时间（秒）")


class StatusResponse(BaseModel):
    """状态响应模型"""
    status: str = Field(..., description="系统状态")
    documents_count: int = Field(..., description="文档数量")
    storage_size: str = Field(..., description="存储大小")
    collection_name: str = Field(..., description="集合名称")
    data_directory: str = Field(..., description="数据目录")


class UploadDocumentResponse(BaseModel):
    """上传文档响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    filename: str = Field(..., description="文件名")
    replaced: bool = Field(..., description="是否替换了同名文件")
    old_chunks: int = Field(..., description="旧文件块数量")
    new_chunks: int = Field(..., description="新文件块数量")
    total_chunks: int = Field(..., description="总文档块数量")


class DeleteDocumentResponse(BaseModel):
    """删除文档响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    filename: str = Field(..., description="文件名")
    deleted_chunks: int = Field(..., description="删除的文档块数量")
    total_chunks: int = Field(..., description="总文档块数量")
