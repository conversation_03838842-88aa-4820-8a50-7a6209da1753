<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试Sources显示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .sources-container {
            margin-top: 15px;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
        }
        /* 复制CSS样式 */
        .source-list {
            margin-top: 12px;
            list-style: none;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .source-item {
            position: relative;
            padding: 6px 10px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            border: 1px solid rgba(173, 30, 35, 0.2);
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
            height: 32px;
            min-width: 120px;
            max-width: fit-content;
            z-index: 1;
        }
        .source-item:hover {
            background: rgba(255, 255, 255, 0.8);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            z-index: 2;
        }
        .source-number {
            font-weight: 600;
            color: #ad1e23;
            margin-right: 6px;
            flex-shrink: 0;
            font-size: 13px;
        }
        .source-title {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #4b5563;
            font-size: 13px;
            max-width: 100px;
        }
        .source-title a {
            color: inherit;
            text-decoration: none;
            transition: color 0.2s ease;
        }
        .source-title a:hover {
            color: #ad1e23;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>调试Sources显示问题</h1>
        
        <button class="test-button" onclick="testAPI()">测试API调用</button>
        <button class="test-button" onclick="testMockData()">测试模拟数据</button>
        
        <div id="result" class="result" style="display: none;">
            <h3>API响应:</h3>
            <pre id="api-response"></pre>
            
            <div id="sources-display" class="sources-container">
                <h4>Sources显示:</h4>
                <div id="sources-content"></div>
            </div>
        </div>
    </div>

    <script>
        // 复制前端代码中的相关函数
        function createSourcesDiv(sources) {
            console.log('createSourcesDiv called with:', sources);
            
            const sourcesDiv = document.createElement("div");
            sourcesDiv.className = "mt-3 space-y-2 text-sm";

            const titleDiv = document.createElement("h4");
            titleDiv.className = "font-semibold text-primary-700";
            titleDiv.textContent = "参考来源：";
            sourcesDiv.appendChild(titleDiv);

            const listDiv = document.createElement("ul");
            listDiv.className = "source-list";

            sources.forEach((source, index) => {
                console.log(`Processing source ${index}:`, source);
                
                const sourceItem = document.createElement("li");
                sourceItem.className = "source-item";

                const numberSpan = document.createElement("span");
                numberSpan.className = "source-number";
                numberSpan.textContent = `[${index + 1}]`;

                const titleSpan = document.createElement("span");
                titleSpan.className = "source-title";

                // 处理源标题和链接
                const title = getSourceTitle(source);
                const url = getSourceUrl(source);
                
                console.log(`Source ${index} - title: ${title}, url: ${url}`);

                if (url) {
                    const link = document.createElement("a");
                    link.href = url;
                    link.target = "_blank";
                    link.rel = "noopener noreferrer";
                    link.textContent = title;
                    titleSpan.appendChild(link);
                } else {
                    titleSpan.textContent = title;
                }

                sourceItem.appendChild(numberSpan);
                sourceItem.appendChild(titleSpan);
                listDiv.appendChild(sourceItem);
            });

            sourcesDiv.appendChild(listDiv);
            return sourcesDiv;
        }

        function getSourceTitle(source) {
            console.log('getSourceTitle called with:', source);
            
            if (source.metadata && source.metadata.title) {
                // 解码Unicode编码
                let title = source.metadata.title;
                if (title.includes("\\u")) {
                    title = title.replace(/\\u[\dA-Fa-f]{4}/g, function (match) {
                        return String.fromCharCode(parseInt(match.replace(/\\u/g, ""), 16));
                    });
                }
                return title;
            }
            return source.metadata?.filename || source.filename || "未知文件";
        }

        function getSourceUrl(source) {
            console.log('getSourceUrl called with:', source);
            return source.metadata?.file_url || null;
        }

        async function testAPI() {
            try {
                console.log('Testing API...');
                const response = await fetch('http://localhost:8000/api/v1/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: '学费信息',
                        max_results: 3
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                console.log('API Response:', data);
                
                document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
                
                if (data.sources && data.sources.length > 0) {
                    const sourcesDiv = createSourcesDiv(data.sources);
                    const container = document.getElementById('sources-content');
                    container.innerHTML = '';
                    container.appendChild(sourcesDiv);
                } else {
                    document.getElementById('sources-content').innerHTML = '<p>没有找到sources数据</p>';
                }
                
                document.getElementById('result').style.display = 'block';
                
            } catch (error) {
                console.error('API Test Error:', error);
                document.getElementById('api-response').textContent = `错误: ${error.message}`;
                document.getElementById('result').style.display = 'block';
            }
        }

        function testMockData() {
            console.log('Testing with mock data...');
            
            const mockData = {
                answer: "这是模拟回答",
                sources: [
                    {
                        content: "模拟内容1...",
                        score: 0.85,
                        metadata: {
                            filename: "test1.txt",
                            title: "测试文档1",
                            file_url: "https://example.com/test1"
                        }
                    },
                    {
                        content: "模拟内容2...",
                        score: 0.75,
                        metadata: {
                            filename: "test2.txt",
                            title: "测试文档2",
                            file_url: null
                        }
                    }
                ]
            };
            
            document.getElementById('api-response').textContent = JSON.stringify(mockData, null, 2);
            
            const sourcesDiv = createSourcesDiv(mockData.sources);
            const container = document.getElementById('sources-content');
            container.innerHTML = '';
            container.appendChild(sourcesDiv);
            
            document.getElementById('result').style.display = 'block';
        }
    </script>
</body>
</html>
