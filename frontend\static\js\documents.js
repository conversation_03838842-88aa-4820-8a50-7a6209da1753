/**
 * 文档管理页面JavaScript
 */

class DocumentManager {
  constructor() {
    this.documents = [];
    this.init();
  }

  init() {
    this.bindEvents();
    this.loadDocuments();
    this.initSyncFeature();
  }

  bindEvents() {
    // 文件选择
    const fileInput = document.getElementById("fileInput");
    fileInput.addEventListener("change", (e) => this.handleFileSelect(e));

    // 刷新按钮
    const refreshBtn = document.getElementById("refreshBtn");
    refreshBtn.addEventListener("click", () => this.loadDocuments());

    // 拖拽上传
    const uploadSection = document.getElementById("uploadSection");
    uploadSection.addEventListener("dragover", (e) => this.handleDragOver(e));
    uploadSection.addEventListener("dragleave", (e) => this.handleDragLeave(e));
    uploadSection.addEventListener("drop", (e) => this.handleDrop(e));
  }

  // 初始化同步功能
  initSyncFeature() {
    const syncBtn = document.getElementById("syncBtn");
    if (syncBtn) {
      syncBtn.addEventListener("click", () => this.startSync());
    }
  }

  // 显示提示信息
  showAlert(message, type = "info") {
    const alertContainer = document.getElementById("alertContainer");
    const alertDiv = document.createElement("div");
    alertDiv.className = `alert ${type}`;
    alertDiv.innerHTML = `
            <i class="fas fa-${
              type === "success"
                ? "check-circle"
                : type === "error"
                ? "exclamation-circle"
                : "info-circle"
            }"></i>
            ${message}
        `;
    alertDiv.style.display = "block";

    alertContainer.innerHTML = "";
    alertContainer.appendChild(alertDiv);

    // 3秒后自动隐藏
    setTimeout(() => {
      alertDiv.style.display = "none";
    }, 3000);
  }

  // 加载文档列表
  async loadDocuments() {
    try {
      const response = await fetch("/api/v1/documents/list");
      const data = await response.json();

      if (data.success) {
        this.documents = data.documents;
        this.renderDocuments();
        this.updateStats(data);
      } else {
        this.showAlert(data.message, "error");
      }
    } catch (error) {
      console.error("加载文档列表失败:", error);
      this.showAlert("加载文档列表失败", "error");
    }
  }

  // 渲染文档列表
  renderDocuments() {
    const container = document.getElementById("documentsContent");
    const statsBar = document.getElementById("statsBar");

    if (this.documents.length === 0) {
      container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3>暂无文档</h3>
                    <p>请上传TXT文件开始使用</p>
                </div>
            `;
      statsBar.style.display = "none";
      return;
    }

    const tableHTML = `
            <table class="documents-table">
                <thead>
                    <tr>
                        <th>文件名</th>
                        <th>文档块数</th>
                        <th>文件大小</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.documents
                      .map(
                        (doc) => `
                        <tr>
                            <td>
                                <div class="file-name">${doc.filename}</div>
                                <div class="file-stats">${doc.file_path}</div>
                            </td>
                            <td>${doc.chunks_count}</td>
                            <td>${this.formatFileSize(doc.file_size)}</td>
                            <td>
                                <button class="delete-btn" onclick="documentManager.deleteDocument('${
                                  doc.filename
                                }')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </td>
                        </tr>
                    `
                      )
                      .join("")}
                </tbody>
            </table>
        `;

    container.innerHTML = tableHTML;
    statsBar.style.display = "flex";
  }

  // 更新统计信息
  updateStats(data) {
    document.getElementById("docCount").textContent = data.documents.length;
    document.getElementById("chunkCount").textContent = data.total_chunks;
    document.getElementById("lastUpdate").textContent =
      new Date().toLocaleString();
  }

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  // 处理文件选择
  handleFileSelect(event) {
    const files = event.target.files;
    this.uploadFiles(files);
  }

  // 处理拖拽
  handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add("dragover");
  }

  handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove("dragover");
  }

  handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove("dragover");
    const files = event.dataTransfer.files;
    this.uploadFiles(files);
  }

  // 上传文件
  async uploadFiles(files) {
    for (let file of files) {
      if (!file.name.endsWith(".txt")) {
        this.showAlert(`文件 ${file.name} 不是TXT格式，已跳过`, "error");
        continue;
      }

      await this.uploadSingleFile(file);
    }

    // 重新加载文档列表
    this.loadDocuments();
  }

  // 上传单个文件
  async uploadSingleFile(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/v1/documents/upload", {
        method: "POST",
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        if (data.replaced) {
          this.showAlert(
            `文件 ${data.filename} 上传成功！替换了同名文件（旧: ${data.old_chunks}块 → 新: ${data.new_chunks}块）<br>已保存到 data 目录`,
            "success"
          );
        } else {
          this.showAlert(
            `文件 ${data.filename} 上传成功！生成了 ${data.new_chunks} 个文档块<br>已保存到 data 目录`,
            "success"
          );
        }
      } else {
        this.showAlert(`上传失败: ${data.message}`, "error");
      }
    } catch (error) {
      console.error("上传文件失败:", error);
      this.showAlert(`上传文件 ${file.name} 失败`, "error");
    }
  }

  // 删除文档
  async deleteDocument(filename) {
    if (
      !confirm(
        `确定要删除文档 "${filename}" 吗？\n\n此操作将删除：\n- ChromaDB中的所有相关向量和文档块\n- docstore中的所有相关节点\n- 所有相关元数据\n- data目录中的文件\n\n此操作不可恢复！`
      )
    ) {
      return;
    }

    try {
      const response = await fetch(
        `/api/v1/documents/${encodeURIComponent(filename)}`,
        {
          method: "DELETE",
        }
      );

      const data = await response.json();

      if (data.success) {
        let message = `文档 ${data.filename} 删除成功！删除了 ${data.deleted_chunks} 个文档块`;
        if (data.file_deleted_from_disk) {
          message += "<br>已从 data 目录删除文件";
        } else {
          message += "<br>仅删除了数据库记录";
        }
        this.showAlert(message, "success");
        this.loadDocuments();
      } else {
        this.showAlert(`删除失败: ${data.message}`, "error");
      }
    } catch (error) {
      console.error("删除文档失败:", error);
      this.showAlert(`删除文档 ${filename} 失败`, "error");
    }
  }

  // 开始同步
  async startSync() {
    const syncBtn = document.getElementById("syncBtn");
    const syncProgress = document.getElementById("syncProgress");

    if (syncBtn.disabled) {
      return;
    }

    // 禁用按钮并显示进度
    syncBtn.disabled = true;
    syncBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
    syncProgress.style.display = "block";

    this.updateProgress(0, "开始同步...", "连接服务器...");

    // 启动进度监控
    console.log("启动进度监控定时器");
    let checkCount = 0;
    const progressInterval = setInterval(async () => {
      checkCount++;
      console.log(`第${checkCount}次检查进度`);
      await this.checkSyncProgress();
    }, 1000); // 改回1秒，便于观察

    try {
      // 异步发起同步请求（不等待完成）
      fetch("/api/v1/sync/chestnut-cms", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })
        .then(async (response) => {
          try {
            if (!response.ok) {
              throw new Error(
                `HTTP ${response.status}: ${response.statusText}`
              );
            }

            const data = await response.json();

            // 停止进度监控
            clearInterval(progressInterval);

            if (data.success) {
              this.updateProgress(100, "同步完成", "处理完成");
              this.showSyncResult(data);
              // 重新加载文档列表
              setTimeout(() => {
                this.loadDocuments();
              }, 1000);
            } else {
              throw new Error(data.message || "同步失败");
            }
          } catch (error) {
            clearInterval(progressInterval);
            console.error("同步失败:", error);
            this.updateProgress(0, "同步失败", error.message);
            this.showAlert(`同步失败: ${error.message}`, "error");
          } finally {
            // 恢复按钮状态
            setTimeout(() => {
              syncBtn.disabled = false;
              syncBtn.innerHTML = '<i class="fas fa-download"></i> 开始同步';
              syncProgress.style.display = "none";
            }, 3000);
          }
        })
        .catch((error) => {
          clearInterval(progressInterval);
          console.error("同步请求失败:", error);
          this.updateProgress(0, "同步失败", error.message);
          this.showAlert(`同步失败: ${error.message}`, "error");

          // 恢复按钮状态
          setTimeout(() => {
            syncBtn.disabled = false;
            syncBtn.innerHTML = '<i class="fas fa-download"></i> 开始同步';
            syncProgress.style.display = "none";
          }, 3000);
        });
    } catch (error) {
      clearInterval(progressInterval);
      console.error("启动同步失败:", error);
      this.updateProgress(0, "同步失败", error.message);
      this.showAlert(`同步失败: ${error.message}`, "error");

      // 恢复按钮状态
      setTimeout(() => {
        syncBtn.disabled = false;
        syncBtn.innerHTML = '<i class="fas fa-download"></i> 开始同步';
        syncProgress.style.display = "none";
      }, 3000);
    }
  }

  // 检查同步进度
  async checkSyncProgress() {
    try {
      console.log("正在检查同步状态...");
      const response = await fetch("/api/v1/sync/status");
      console.log("状态API响应:", response.status, response.statusText);

      if (response.ok) {
        const status = await response.json();
        console.log("完整同步状态:", JSON.stringify(status, null, 2));

        if (status.is_syncing) {
          const progress =
            status.total_items > 0
              ? Math.round((status.current_progress / status.total_items) * 100)
              : 0;

          console.log(
            `计算进度: ${status.current_progress}/${status.total_items} = ${progress}%`
          );

          this.updateProgress(
            progress,
            `处理中 (${status.current_progress}/${status.total_items})`,
            status.current_item || "处理中..."
          );

          console.log(`✅ 进度已更新: ${progress}% - ${status.current_item}`);
        } else {
          console.log("❌ 同步未进行中，is_syncing =", status.is_syncing);
        }
      } else {
        console.error("状态API请求失败:", response.status, response.statusText);
      }
    } catch (error) {
      console.error("获取同步状态异常:", error);
    }
  }

  // 更新进度显示
  updateProgress(percent, text, currentItem) {
    console.log(`🔄 updateProgress调用: ${percent}%, ${text}, ${currentItem}`);

    const progressBar = document.getElementById("progressBar");
    const progressText = document.getElementById("progressText");
    const progressPercent = document.getElementById("progressPercent");
    const currentItemElement = document.getElementById("currentItem");

    if (progressBar) {
      progressBar.style.width = `${percent}%`;
      console.log(`进度条宽度设置为: ${percent}%`);
    } else {
      console.error("找不到progressBar元素");
    }

    if (progressText) {
      progressText.textContent = text;
      console.log(`进度文本设置为: ${text}`);
    } else {
      console.error("找不到progressText元素");
    }

    if (progressPercent) {
      progressPercent.textContent = `${percent}%`;
      console.log(`进度百分比设置为: ${percent}%`);
    } else {
      console.error("找不到progressPercent元素");
    }

    if (currentItemElement) {
      currentItemElement.textContent = currentItem;
      console.log(`当前项目设置为: ${currentItem}`);
    } else {
      console.error("找不到currentItem元素");
    }
  }

  // 显示同步结果
  showSyncResult(data) {
    const stats = data.statistics || {};
    const processingTime = data.processing_time || 0;

    let message = `同步完成！处理时间: ${processingTime.toFixed(2)}秒<br>`;
    message += `新增: ${stats.added || 0}篇，更新: ${
      stats.updated || 0
    }篇，删除: ${stats.deleted || 0}篇，保持: ${stats.kept || 0}篇`;

    if (stats.errors > 0) {
      message += `<br>错误: ${stats.errors}篇`;
      this.showAlert(message, "error");
    } else {
      this.showAlert(message, "success");
    }

    // 如果有错误详情，在控制台显示
    if (data.errors && data.errors.length > 0) {
      console.warn("同步过程中的错误:", data.errors);
    }
  }
}

// 初始化文档管理器
const documentManager = new DocumentManager();
