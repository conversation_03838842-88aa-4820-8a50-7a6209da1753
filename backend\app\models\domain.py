"""
领域模型定义
"""
from typing import List
from pydantic import BaseModel, Field


class DocumentInfo(BaseModel):
    """文档信息模型"""
    filename: str = Field(..., description="文件名")
    chunks_count: int = Field(..., description="文档块数量")
    file_size: int = Field(..., description="文件大小（字节）")
    file_path: str = Field(..., description="文件路径")


class DocumentsListResponse(BaseModel):
    """文档列表响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    documents: List[DocumentInfo] = Field(..., description="文档列表")
    total_chunks: int = Field(..., description="总文档块数量")
