#!/usr/bin/env python3
"""
测试混合检索API的完整功能
"""

import requests
import json
import time

def test_hybrid_api():
    """测试混合检索API"""
    base_url = "http://localhost:8000/api/v1"
    
    # 测试用例
    test_cases = [
        {
            "name": "默认混合检索",
            "data": {
                "query": "什么是人工智能？",
                "max_results": 3
            }
        },
        {
            "name": "自定义权重混合检索",
            "data": {
                "query": "什么是人工智能？",
                "max_results": 3,
                "bm25_weight": 0.3,
                "vector_weight": 0.7
            }
        },
        {
            "name": "纯向量检索",
            "data": {
                "query": "什么是人工智能？",
                "max_results": 3,
                "bm25_weight": 0.0,
                "vector_weight": 1.0
            }
        },
        {
            "name": "纯BM25检索",
            "data": {
                "query": "什么是人工智能？",
                "max_results": 3,
                "bm25_weight": 1.0,
                "vector_weight": 0.0
            }
        },
        {
            "name": "无效权重测试",
            "data": {
                "query": "什么是人工智能？",
                "max_results": 3,
                "bm25_weight": 0.3,
                "vector_weight": 0.8  # 总和不为1
            }
        }
    ]
    
    print("🔍 开始测试混合检索API")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            response = requests.post(
                f"{base_url}/query",
                json=test_case["data"],
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 请求成功")
                print(f"检索模式: {result.get('retrieval_mode', 'N/A')}")
                print(f"权重配置: {result.get('weights_used', 'N/A')}")
                print(f"处理时间: {result.get('processing_time', 0):.3f}秒")
                print(f"源文档数: {result.get('total_sources', 0)}")
                
                if result.get('answer'):
                    answer_preview = result['answer'][:100] + "..." if len(result['answer']) > 100 else result['answer']
                    print(f"回答预览: {answer_preview}")
                
            elif response.status_code == 400:
                error_detail = response.json().get('detail', 'Unknown error')
                print(f"⚠️  客户端错误: {error_detail}")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
        except requests.exceptions.ConnectionError:
            print("🔌 连接失败 - 请确保服务器正在运行")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 API测试完成")

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔄 测试向后兼容性")
    print("-" * 40)
    
    # 使用旧格式的请求（不包含权重参数）
    old_format_request = {
        "query": "测试向后兼容性",
        "max_results": 2
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/query",
            json=old_format_request,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 向后兼容性测试通过")
            print(f"默认检索模式: {result.get('retrieval_mode', 'N/A')}")
            print(f"默认权重: {result.get('weights_used', 'N/A')}")
        else:
            print(f"❌ 向后兼容性测试失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 向后兼容性测试异常: {e}")

if __name__ == "__main__":
    test_hybrid_api()
    test_backward_compatibility()
