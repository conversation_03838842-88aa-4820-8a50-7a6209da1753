<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>文档管理 - RAG聊天应用</title>
    <link rel="stylesheet" href="/static/css/style.css" />
    <link rel="stylesheet" href="/static/css/documents.css" />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="documents-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title">
          <i class="fas fa-folder-open"></i>
          文档管理
        </h1>
        <div class="nav-links">
          <a href="/" class="nav-link">
            <i class="fas fa-comments"></i> 返回聊天
          </a>
        </div>
      </div>

      <!-- 提示信息 -->
      <div id="alertContainer"></div>

      <!-- 文件上传区域 -->
      <div class="upload-section" id="uploadSection">
        <div class="upload-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <div class="upload-text">
          拖拽TXT文件到此处，或点击按钮选择文件上传
          <br />
          <small>支持同名文件替换，系统会自动删除旧文件的所有数据</small>
        </div>
        <div class="file-input-wrapper">
          <input
            type="file"
            id="fileInput"
            class="file-input"
            accept=".txt"
            multiple
          />
          <button class="upload-btn">
            <i class="fas fa-plus"></i> 选择文件
          </button>
        </div>
      </div>

      <!-- 数据同步区域 -->
      <div class="sync-section">
        <div class="sync-header">
          <h2 class="sync-title">
            <i class="fas fa-sync-alt"></i>
            CMS数据同步
          </h2>
          <button class="sync-btn" id="syncBtn">
            <i class="fas fa-download"></i>
            开始同步
          </button>
        </div>
        <div class="sync-description">
          <p>
            从ChestnutCMS数据库同步最新文章到RAG系统，支持增删改查的全量同步。
          </p>
          <small>同步过程中会显示详细进度，请耐心等待。</small>
        </div>

        <!-- 同步进度 -->
        <div class="sync-progress" id="syncProgress">
          <div class="progress-info">
            <span id="progressText">准备中...</span>
            <span id="progressPercent">0%</span>
          </div>
          <div class="progress-bar-container">
            <div class="progress-bar" id="progressBar"></div>
          </div>
          <div class="current-item" id="currentItem">等待开始...</div>
        </div>
      </div>

      <!-- 文档列表 -->
      <div class="documents-list">
        <div class="list-header">
          <h2 class="list-title">已上传文档</h2>
          <button class="refresh-btn" id="refreshBtn">
            <i class="fas fa-sync-alt"></i> 刷新
          </button>
        </div>

        <div id="documentsContent">
          <div class="loading">
            <div><i class="fas fa-spinner fa-spin"></i></div>
            <div>加载中...</div>
          </div>
        </div>

        <div class="stats-bar" id="statsBar" style="display: none">
          <div class="stat-item">
            <i class="fas fa-file-text"></i>
            <span>文档数量: <span id="docCount">0</span></span>
          </div>
          <div class="stat-item">
            <i class="fas fa-cubes"></i>
            <span>总块数: <span id="chunkCount">0</span></span>
          </div>
          <div class="stat-item">
            <i class="fas fa-clock"></i>
            <span>最后更新: <span id="lastUpdate">-</span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- JavaScript -->
    <script src="/static/js/documents.js"></script>
  </body>
</html>
