#!/usr/bin/env python3
"""
检查BM25检索所需的数据状态
判断是否需要重新加载文档
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_docstore_status():
    """检查docstore中的数据状态"""
    print("\n" + "="*60)
    print("📊 检查BM25检索数据准备状态")
    print("="*60)
    
    try:
        from backend.app.services.rag_service import RAGService
        
        print("🚀 正在初始化RAG服务...")
        rag_service = RAGService()
        
        if not rag_service.index:
            print("❌ 索引未初始化")
            return False
        
        # 检查docstore
        docstore = rag_service.index.docstore
        if not hasattr(docstore, 'docs'):
            print("❌ docstore结构异常")
            return False
        
        doc_ids = list(docstore.docs.keys())
        print(f"📄 docstore中的文档节点数量: {len(doc_ids)}")
        
        if len(doc_ids) == 0:
            print("❌ docstore为空，需要重新加载文档")
            print("\n🔧 解决方案:")
            print("1. 访问 http://localhost:8000/api/v1/documents/load")
            print("2. 或运行: curl -X POST http://localhost:8000/api/v1/documents/load")
            return False
        
        # 检查文本内容质量
        valid_nodes = 0
        empty_nodes = 0
        total_text_length = 0
        
        print(f"\n🔍 检查前{min(5, len(doc_ids))}个节点的内容:")
        
        for i, doc_id in enumerate(doc_ids[:5]):
            try:
                node = docstore.get_node(doc_id)
                text_length = len(node.text) if hasattr(node, 'text') and node.text else 0
                
                if text_length > 0:
                    valid_nodes += 1
                    total_text_length += text_length
                    preview = node.text[:100].replace('\n', ' ')
                    print(f"  节点 {i+1}: ✅ {text_length}字符 - '{preview}...'")
                else:
                    empty_nodes += 1
                    print(f"  节点 {i+1}: ❌ 空文本")
                    
            except Exception as e:
                empty_nodes += 1
                print(f"  节点 {i+1}: ❌ 读取失败 - {e}")
        
        # 统计所有节点
        for doc_id in doc_ids:
            try:
                node = docstore.get_node(doc_id)
                if hasattr(node, 'text') and node.text and len(node.text.strip()) > 0:
                    valid_nodes += 1
                    total_text_length += len(node.text)
                else:
                    empty_nodes += 1
            except:
                empty_nodes += 1
        
        print(f"\n📈 整体统计:")
        print(f"  - 总节点数: {len(doc_ids)}")
        print(f"  - 有效节点: {valid_nodes}")
        print(f"  - 空节点: {empty_nodes}")
        print(f"  - 平均文本长度: {total_text_length/max(valid_nodes, 1):.0f}字符")
        print(f"  - 有效率: {valid_nodes/(valid_nodes+empty_nodes)*100:.1f}%")
        
        # 判断数据质量
        if valid_nodes == 0:
            print("\n❌ 没有有效的文本节点，BM25无法工作")
            print("🔧 需要重新加载文档")
            return False
        elif empty_nodes > valid_nodes * 0.2:  # 如果空节点超过20%
            print(f"\n⚠️  空节点比例较高({empty_nodes}/{len(doc_ids)})")
            print("🔧 建议重新加载文档以确保最佳效果")
            return "warning"
        else:
            print(f"\n✅ 数据质量良好，BM25可以正常工作")
            return True
            
    except Exception as e:
        print(f"❌ 检查过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_bm25_functionality():
    """测试BM25功能是否可用"""
    print("\n" + "="*60)
    print("🧪 测试BM25检索功能")
    print("="*60)
    
    try:
        from backend.app.services.rag_service import RAGService
        from llama_index.retrievers.bm25 import BM25Retriever
        
        rag_service = RAGService()
        
        if not rag_service.index:
            print("❌ 索引未初始化")
            return False
        
        # 尝试创建BM25检索器
        print("🔄 正在创建BM25检索器...")
        bm25_retriever = BM25Retriever.from_defaults(
            docstore=rag_service.index.docstore,
            similarity_top_k=2
        )
        print("✅ BM25检索器创建成功")
        
        # 测试检索功能
        print("🔍 测试BM25检索...")
        from llama_index.core import QueryBundle
        test_query = "测试"
        query_bundle = QueryBundle(query_str=test_query)
        
        results = bm25_retriever.retrieve(query_bundle)
        print(f"✅ BM25检索成功，返回 {len(results)} 个结果")
        
        if len(results) > 0:
            print("📋 检索结果示例:")
            for i, result in enumerate(results):
                score = getattr(result, 'score', 'N/A')
                text_preview = result.node.text[:50].replace('\n', ' ') if result.node.text else "无文本"
                print(f"  结果 {i+1}: 评分={score}, 文本='{text_preview}...'")
        
        return True
        
    except Exception as e:
        print(f"❌ BM25功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🔍 BM25检索数据状态检查")
    
    # 检查docstore状态
    docstore_status = check_docstore_status()
    
    # 如果docstore有数据，测试BM25功能
    if docstore_status:
        bm25_status = test_bm25_functionality()
    else:
        bm25_status = False
    
    # 总结和建议
    print("\n" + "="*60)
    print("📋 检查结果总结")
    print("="*60)
    
    if docstore_status is True and bm25_status:
        print("🎉 恭喜！您的系统已准备好使用BM25混合检索")
        print("✅ 无需重新上传文档")
        print("✅ 可以直接使用混合检索功能")
        
    elif docstore_status == "warning":
        print("⚠️  系统基本可用，但建议优化")
        print("🔧 建议重新加载文档以获得最佳效果")
        print("📝 重新加载方法:")
        print("   - 访问: http://localhost:8000/api/v1/documents/load")
        print("   - 或运行: curl -X POST http://localhost:8000/api/v1/documents/load")
        
    else:
        print("❌ 系统尚未准备好使用BM25检索")
        print("🔧 必须重新加载文档")
        print("📝 解决步骤:")
        print("1. 确保data目录中有TXT文件")
        print("2. 访问: http://localhost:8000/api/v1/documents/load")
        print("3. 等待文档加载完成")
        print("4. 重新运行此检查脚本")
    
    print("="*60)


if __name__ == "__main__":
    main()
