#!/usr/bin/env python3
"""
测试API返回的sources数据结构
"""
import requests
import json

def test_query_api():
    """测试查询API"""
    url = "http://localhost:8000/api/v1/query"
    data = {
        "query": "贵阳人文科技学院",
        "max_results": 3,
        "bm25_weight": 0.5,
        "vector_weight": 0.5
    }
    
    try:
        print("🔍 测试查询API...")
        print(f"URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
        
        response = requests.post(url, json=data, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 查询成功!")
            print(f"答案长度: {len(result.get('answer', ''))}")
            print(f"源文档数: {len(result.get('sources', []))}")
            
            # 详细检查sources结构
            sources = result.get('sources', [])
            print(f"\n📋 Sources详细信息:")
            for i, source in enumerate(sources):
                print(f"\n--- Source {i+1} ---")
                print(f"类型: {type(source)}")
                print(f"键: {list(source.keys()) if isinstance(source, dict) else 'N/A'}")
                
                if isinstance(source, dict):
                    print(f"content: {source.get('content', 'N/A')[:100]}...")
                    print(f"score: {source.get('score', 'N/A')}")
                    
                    metadata = source.get('metadata', {})
                    print(f"metadata类型: {type(metadata)}")
                    if isinstance(metadata, dict):
                        print(f"metadata键: {list(metadata.keys())}")
                        print(f"filename: {metadata.get('filename', 'N/A')}")
                        print(f"title: {metadata.get('title', 'N/A')}")
                        print(f"file_url: {metadata.get('file_url', 'N/A')}")
                    else:
                        print(f"metadata内容: {metadata}")
                else:
                    print(f"source内容: {source}")
            
            return True
        else:
            print(f"❌ 查询失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    test_query_api()
