"""
应用事件处理
"""
import sys
import os
from typing import Callable

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from backend.config.logging_config import get_logger
from backend.app.dependencies import initialize_services, cleanup_services

logger = get_logger(__name__)


def create_start_app_handler() -> Callable:
    """创建应用启动事件处理器"""
    async def start_app() -> None:
        logger.info("应用启动中...")
        await initialize_services()
        logger.info("应用启动完成")
    
    return start_app


def create_stop_app_handler() -> Callable:
    """创建应用停止事件处理器"""
    async def stop_app() -> None:
        logger.info("应用停止中...")
        await cleanup_services()
        logger.info("应用停止完成")
    
    return stop_app
