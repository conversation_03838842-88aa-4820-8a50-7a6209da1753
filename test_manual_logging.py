#!/usr/bin/env python3
"""
手动测试日志系统
直接调用各个模块的logger来验证日志输出
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.config.logging_config import setup_logging, get_logger


def test_manual_logging():
    """手动测试各个模块的日志"""
    print("🧪 手动测试日志系统")
    print("=" * 60)
    
    # 设置DEBUG级别
    setup_logging("DEBUG")
    
    # 测试各个模块的logger
    modules = [
        "backend.app.main",
        "backend.app.services.rag_service", 
        "backend.app.services.chestnut_cms_service",
        "backend.app.services.mysql_service",
        "backend.app.core.middleware",
        "backend.app.core.exceptions",
        "backend.app.core.events",
        "backend.app.dependencies"
    ]
    
    for module_name in modules:
        print(f"\n📝 测试模块: {module_name}")
        logger = get_logger(module_name)
        
        logger.debug(f"🔍 DEBUG - {module_name}")
        logger.info(f"ℹ️ INFO - {module_name}")
        logger.warning(f"⚠️ WARNING - {module_name}")
        logger.error(f"❌ ERROR - {module_name}")
    
    print("\n" + "=" * 60)
    print("✅ 手动日志测试完成")
    print("📋 如果您看到了所有级别的日志，说明日志系统工作正常")


if __name__ == "__main__":
    test_manual_logging()
