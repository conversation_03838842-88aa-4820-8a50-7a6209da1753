# ===========================================
# 应用基础配置
# ===========================================
APP_NAME="RAG Chat Application"
APP_VERSION="1.0.0"
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=false
ENVIRONMENT=production

# ===========================================
# API配置
# ===========================================
API_V1_PREFIX="/api/v1"

# ===========================================
# OpenAI API配置
# ===========================================
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini
EMBEDDING_MODEL=text-embedding-3-small

# ===========================================
# 存储配置
# ===========================================
DATA_DIR=./data
COLLECTION_NAME=documents

# ===========================================
# Qdrant配置
# ===========================================
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_GRPC_PORT=6334
QDRANT_API_KEY=
QDRANT_PREFER_GRPC=true

# ===========================================
# 数据库配置（CMS模块）
# ===========================================
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password_here
MYSQL_DATABASE=chestnut_cms

# ===========================================
# CORS配置
# ===========================================
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:8000", "http://127.0.0.1:8000", "https://chat.example.org"]
