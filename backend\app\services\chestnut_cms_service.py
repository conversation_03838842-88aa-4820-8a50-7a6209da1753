"""
ChestnutCMS数据同步服务
用于从ChestnutCMS数据库同步文章到RAG系统
"""
import re
import html
import time
import logging
from typing import Dict, List, Any
from datetime import datetime
from bs4 import BeautifulSoup
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from backend.config.settings import settings
from backend.config.logging_config import get_logger
from backend.app.services.mysql_service import MySQLService
from backend.app.services.rag_service import RAGService

logger = get_logger(__name__)


class ChestnutCMSService:
    """ChestnutCMS数据同步服务类"""
    
    def __init__(self):
        self.mysql_service = MySQLService()
        self.rag_service = RAGService()
        self.sync_status = {
            "is_syncing": False,
            "current_progress": 0,
            "total_items": 0,
            "current_item": "",
            "start_time": None,
            "estimated_completion": None,
            "errors": []
        }
        logger.info("ChestnutCMS同步服务初始化完成")
    
    def clean_html_content(self, html_content: str) -> str:
        """
        清理HTML内容，提取纯文本
        
        Args:
            html_content (str): HTML内容
            
        Returns:
            str: 清理后的文本内容
        """
        if not html_content:
            return ""
        
        try:
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除script和style标签
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 移除图片、视频、音频等多媒体标签
            for media in soup(["img", "video", "audio", "iframe", "object", "embed"]):
                media.decompose()
            
            # 移除表格（保留表格中的文本）
            for table in soup.find_all("table"):
                table_text = table.get_text(separator=" ", strip=True)
                if table_text:
                    table.replace_with(table_text)
            
            # 获取文本内容
            text = soup.get_text()
            
            # 清理HTML实体
            text = html.unescape(text)
            
            # 处理换行和空格
            text = re.sub(r'\n\s*\n', '\n\n', text)  # 合并多个空行
            text = re.sub(r'[ \t]+', ' ', text)  # 合并多个空格
            text = text.strip()
            
            return text
            
        except Exception as e:
            logger.error(f"HTML清理失败: {e}")
            # 如果解析失败，尝试简单的正则表达式清理
            text = re.sub(r'<[^>]+>', '', html_content)
            text = html.unescape(text)
            return text.strip()
    
    def build_article_url(self, content_id: str, path: str) -> str:
        """
        构建文章URL
        
        Args:
            content_id (str): 文章ID
            path (str): 分类路径
            
        Returns:
            str: 文章URL
        """
        if not path:
            path = "article"
        
        # 确保路径以/开头
        if not path.startswith('/'):
            path = '/' + path
            
        # 确保路径以/结尾
        if not path.endswith('/'):
            path = path + '/'
        
        # 构建完整URL: base_url + path + content_id + .shtml
        url = f"{settings.chestnut_cms_base_url}{path}{content_id}.shtml"
        return url
    
    def get_rag_articles_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取RAG系统中现有文章信息
        
        Returns:
            Dict[str, Dict[str, Any]]: 以content_id为key的文章信息字典
        """
        try:
            result = self.rag_service.get_documents_list()
            if not result["success"]:
                return {}
            
            rag_articles = {}
            for doc in result["documents"]:
                filename = doc["filename"]
                if filename.endswith(".txt"):
                    content_id = filename.replace(".txt", "")

                    # 从Qdrant返回的文档信息中直接获取元数据
                    publish_date = doc.get("publish_date")

                    rag_articles[content_id] = {
                        "filename": filename,
                        "chunks_count": doc["chunks_count"],
                        "publish_date": publish_date,
                        "title": doc.get("title"),
                        "content_id": doc.get("content_id"),
                        "file_url": doc.get("file_url"),
                        "source": doc.get("source")
                    }
            
            logger.info(f"RAG系统中已有 {len(rag_articles)} 篇文章")
            return rag_articles
            
        except Exception as e:
            logger.error(f"获取RAG文章信息失败: {e}")
            return {}
    
    def compare_articles(self, cms_articles: List[Dict[str, Any]], rag_articles: Dict[str, Dict[str, Any]]) -> Dict[str, List]:
        """
        比较CMS和RAG系统中的文章，确定同步操作

        Args:
            cms_articles (List[Dict[str, Any]]): CMS文章列表
            rag_articles (Dict[str, Dict[str, Any]]): RAG文章字典

        Returns:
            Dict[str, List]: 同步操作分类
        """
        operations = {
            "to_add": [],       # 需要新增的文章
            "to_update": [],    # 需要更新的文章
            "to_delete": [],    # 需要删除的文章
            "to_keep": []       # 保持不变的文章
        }

        # 构建CMS文章字典（MySQL已经过滤了link_flag不为Null的文章）
        qualified_cms_articles = {}
        for article in cms_articles:
            content_id = str(article["content_id"])
            qualified_cms_articles[content_id] = article

        # 在合格文章中进行增删改判断
        for content_id, cms_article in qualified_cms_articles.items():
            if content_id not in rag_articles:
                # 新增文章
                operations["to_add"].append(cms_article)
                logger.debug(f"新增文章: {content_id} - {cms_article.get('title', '无标题')}")
            else:
                # 检查是否需要更新
                rag_article = rag_articles[content_id]
                cms_date = str(cms_article["publish_date"]) if cms_article["publish_date"] else None
                rag_date = rag_article["publish_date"]

                logger.debug(f"比较文章 {content_id}: CMS日期='{cms_date}' (类型: {type(cms_date)}), RAG日期='{rag_date}' (类型: {type(rag_date)})")

                # 确保两个日期都是字符串格式进行比较
                if cms_date != rag_date:
                    # 需要更新
                    operations["to_update"].append(cms_article)
                    logger.debug(f"更新文章: {content_id} - {cms_article.get('title', '无标题')} (日期不匹配: '{cms_date}' != '{rag_date}')")
                else:
                    # 保持不变
                    operations["to_keep"].append(cms_article)
                    logger.debug(f"保持文章: {content_id} - {cms_article.get('title', '无标题')} (日期匹配: '{cms_date}')")

        # 检查需要删除的文章（RAG中存在但合格CMS文章中不存在）
        for content_id, rag_article in rag_articles.items():
            if content_id not in qualified_cms_articles:
                operations["to_delete"].append(rag_article)

        logger.info(f"同步操作统计: 新增={len(operations['to_add'])}, 更新={len(operations['to_update'])}, 删除={len(operations['to_delete'])}, 保持={len(operations['to_keep'])}")

        return operations
    
    def process_article(self, article: Dict[str, Any], operation: str) -> Dict[str, Any]:
        """
        处理单个文章
        
        Args:
            article (Dict[str, Any]): 文章数据
            operation (str): 操作类型 (add, update, delete)
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            content_id = str(article["content_id"])
            title = article["title"] or f"文章{content_id}"
            
            if operation == "delete":
                # 删除文章
                filename = f"{content_id}.txt"
                result = self.rag_service.delete_document(filename)
                return {
                    "success": result["success"],
                    "content_id": content_id,
                    "title": title,
                    "operation": "delete",
                    "message": result["message"]
                }
            
            # 对于add和update操作，需要处理文章内容
            html_content = article.get("content_html", "")
            if not html_content:
                error_msg = f"文章内容为空: content_id={content_id}, title='{title}'"
                logger.warning(error_msg)
                return {
                    "success": False,
                    "content_id": content_id,
                    "title": title,
                    "operation": operation,
                    "message": "文章内容为空"
                }
            
            # 清理HTML内容
            clean_text = self.clean_html_content(html_content)
            if not clean_text:
                # 如果内容为空，使用标题作为内容，避免跳过文章
                clean_text = f"标题: {title}"
                logger.warning(f"文章 {content_id} 内容为空，使用标题作为内容")
            
            # 构建文章URL
            path = article.get("path", "")
            file_url = self.build_article_url(content_id, path)
            
            # 准备文档内容
            filename = f"{content_id}.txt"
            
            # 创建文档内容（包含标题和正文）
            document_content = f"标题: {title}\n\n{clean_text}"
            
            # 准备扩展元数据
            additional_metadata = {
                "content_id": content_id,
                "title": title,
                "file_url": file_url,
                "publish_date": str(article["publish_date"]) if article["publish_date"] else None,
                "source": "chestnut_cms"
            }
            
            # 上传文档到RAG系统（传入扩展元数据）
            result = self.rag_service.upload_document(document_content, filename, additional_metadata)
            
            if result["success"]:
                return {
                    "success": True,
                    "content_id": content_id,
                    "title": title,
                    "operation": operation,
                    "message": f"文章处理成功: {title}",
                    "file_url": file_url,
                    "chunks": result.get("new_chunks", 0)
                }
            else:
                return {
                    "success": False,
                    "content_id": content_id,
                    "title": title,
                    "operation": operation,
                    "message": f"文章处理失败: {result['message']}"
                }
            
        except Exception as e:
            logger.error(f"处理文章失败: {e}")
            return {
                "success": False,
                "content_id": article.get("content_id", "未知"),
                "title": article.get("title", "未知"),
                "operation": operation,
                "message": f"处理失败: {str(e)}"
            }

    def _get_qdrant_document_metadata(self, filename: str) -> Dict[str, Any]:
        """
        从Qdrant获取文档的元数据

        Args:
            filename (str): 文件名

        Returns:
            Dict[str, Any]: 文档元数据，如果不存在返回空字典
        """
        try:
            if not self.rag_service.qdrant_client:
                return {}

            from qdrant_client.http.models import Filter, FieldCondition, MatchValue
            from backend.config.settings import settings

            # 查询第一个匹配的点来获取元数据
            search_result = self.rag_service.qdrant_client.scroll(
                collection_name=settings.collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="filename",
                            match=MatchValue(value=filename)
                        )
                    ]
                ),
                limit=1,
                with_payload=True,
                with_vectors=False
            )

            if search_result[0]:
                point = search_result[0][0]
                return point.payload or {}

            return {}

        except Exception as e:
            logger.debug(f"获取Qdrant文档元数据失败: {e}")
            return {}
    

    
    def sync_articles(self) -> Dict[str, Any]:
        """
        同步ChestnutCMS文章
        
        Returns:
            Dict[str, Any]: 同步结果
        """
        if self.sync_status["is_syncing"]:
            return {
                "success": False,
                "message": "同步正在进行中，请稍后再试"
            }
        
        try:
            # 开始同步
            self.sync_status["is_syncing"] = True
            self.sync_status["start_time"] = datetime.now().isoformat()
            self.sync_status["errors"] = []
            
            logger.info("开始同步ChestnutCMS文章...")
            
            # 获取CMS文章
            self.sync_status["current_item"] = "获取CMS文章列表"
            cms_articles = self.mysql_service.get_chestnut_cms_articles()
            
            # 获取RAG文章
            self.sync_status["current_item"] = "获取RAG文章列表"
            rag_articles = self.get_rag_articles_info()
            
            # 比较并确定操作
            logger.info("开始比较CMS和RAG文章...")
            logger.info(f"CMS文章数量: {len(cms_articles)}")
            logger.info(f"RAG文章数量: {len(rag_articles)}")

            # 临时启用DEBUG级别日志
            original_level = logger.level
            logger.setLevel(logging.DEBUG)

            operations = self.compare_articles(cms_articles, rag_articles)

            # 恢复原来的日志级别
            logger.setLevel(original_level)

            logger.info("文章比较完成")

            # 计算总任务数（只包含实际需要处理的文章）
            total_tasks = len(operations["to_add"]) + len(operations["to_update"]) + len(operations["to_delete"])
            self.sync_status["total_items"] = total_tasks
            self.sync_status["current_progress"] = 0

            # 处理结果统计
            statistics = {
                "total_processed": 0,
                "added": 0,
                "updated": 0,
                "deleted": 0,
                "kept": 0,
                "errors": 0
            }
            
            processed_details = []
            
            # 处理删除操作
            for article in operations["to_delete"]:
                self.sync_status["current_item"] = f"删除文章: {article['filename']}"
                result = self.process_article(article, "delete")
                processed_details.append(result)
                
                if result["success"]:
                    statistics["deleted"] += 1
                else:
                    statistics["errors"] += 1
                    self.sync_status["errors"].append(result["message"])
                
                statistics["total_processed"] += 1
                self.sync_status["current_progress"] += 1

                # 添加小延迟，让前端能够捕获进度状态
                time.sleep(0.1)
            
            # 处理新增操作
            for i, article in enumerate(operations["to_add"]):
                self.sync_status["current_item"] = f"新增文章: {article['title']}"
                logger.info(f"开始处理新增文章 {i+1}/{len(operations['to_add'])}: {article['content_id']} - {article['title']}")

                article_start_time = time.time()
                result = self.process_article(article, "add")
                article_time = time.time() - article_start_time

                processed_details.append(result)

                if result["success"]:
                    statistics["added"] += 1
                    logger.info(f"✅ 新增成功: {result['content_id']} - {result['title']} (耗时: {article_time:.2f}秒)")
                else:
                    statistics["errors"] += 1
                    self.sync_status["errors"].append(result["message"])
                    logger.error(f"❌ 新增失败: {result['content_id']} - {result['title']} - 原因: {result['message']} (耗时: {article_time:.2f}秒)")

                statistics["total_processed"] += 1
                self.sync_status["current_progress"] += 1

                # 添加小延迟，让前端能够捕获进度状态
                time.sleep(0.1)
            
            # 处理更新操作
            for article in operations["to_update"]:
                self.sync_status["current_item"] = f"更新文章: {article['title']}"
                result = self.process_article(article, "update")
                processed_details.append(result)
                
                if result["success"]:
                    statistics["updated"] += 1
                else:
                    statistics["errors"] += 1
                    self.sync_status["errors"].append(result["message"])
                
                statistics["total_processed"] += 1
                self.sync_status["current_progress"] += 1

                # 添加小延迟，让前端能够捕获进度状态
                time.sleep(0.1)
            
            # 保持不变的文章数量
            statistics["kept"] = len(operations["to_keep"])

            # 完成同步
            self.sync_status["is_syncing"] = False
            self.sync_status["current_item"] = "同步完成"

            processing_time = (datetime.now() - datetime.fromisoformat(self.sync_status["start_time"])).total_seconds()

            success_message = f"同步完成! 处理了 {statistics['total_processed']} 篇文章"
            if statistics["errors"] > 0:
                success_message += f"，其中 {statistics['errors']} 篇失败"
            
            logger.info(success_message)
            
            return {
                "success": True,
                "message": success_message,
                "statistics": statistics,
                "processing_time": processing_time,
                "errors": self.sync_status["errors"],
                "processed_details": processed_details
            }
            
        except Exception as e:
            # 同步失败
            self.sync_status["is_syncing"] = False
            self.sync_status["current_item"] = f"同步失败: {str(e)}"
            self.sync_status["errors"].append(str(e))
            
            logger.error(f"同步失败: {e}")
            return {
                "success": False,
                "message": f"同步失败: {str(e)}",
                "statistics": {},
                "processing_time": 0,
                "errors": [str(e)]
            }
    
    def get_sync_status(self) -> Dict[str, Any]:
        """
        获取同步状态
        
        Returns:
            Dict[str, Any]: 同步状态
        """
        return dict(self.sync_status)
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试与ChestnutCMS的连接
        
        Returns:
            Dict[str, Any]: 连接测试结果
        """
        try:
            # 测试MySQL连接
            mysql_result = self.mysql_service.test_connection()
            if not mysql_result["success"]:
                return {
                    "success": False,
                    "message": "MySQL连接失败",
                    "mysql_error": mysql_result["message"]
                }
            
            # 测试获取文章统计
            summary = self.mysql_service.get_chestnut_cms_articles_summary()
            
            return {
                "success": True,
                "message": "连接测试成功",
                "mysql_info": mysql_result["connection_info"],
                "articles_summary": summary
            }
            
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return {
                "success": False,
                "message": f"连接测试失败: {str(e)}"
            }


# 创建全局实例
chestnut_cms_service = ChestnutCMSService()


def get_chestnut_cms_service() -> ChestnutCMSService:
    """
    获取ChestnutCMS服务实例（用于依赖注入）
    
    Returns:
        ChestnutCMSService: 服务实例
    """
    return chestnut_cms_service