"""
请求模型定义
"""
from pydantic import BaseModel, Field, field_validator
from typing import Optional


class QueryRequest(BaseModel):
    """查询请求模型"""
    query: str = Field(..., description="用户问题", min_length=1, max_length=1000)
    max_results: int = Field(5, description="最大返回结果数", ge=1, le=20)
    similarity_threshold: float = Field(0.7, description="相似度阈值", ge=0.0, le=1.0)

    # 混合检索权重参数
    bm25_weight: Optional[float] = Field(
        0.5,
        description="BM25检索权重",
        ge=0.0,
        le=1.0
    )
    vector_weight: Optional[float] = Field(
        0.5,
        description="向量检索权重",
        ge=0.0,
        le=1.0
    )

    @field_validator('vector_weight')
    @classmethod
    def validate_weights(cls, v, info):
        """验证权重总和为1.0"""
        if 'bm25_weight' in info.data:
            bm25_weight = info.data['bm25_weight']
            if bm25_weight is not None and v is not None:
                total = bm25_weight + v
                if abs(total - 1.0) > 1e-6:  # 允许浮点数精度误差
                    raise ValueError(f"BM25权重({bm25_weight})和向量权重({v})之和必须等于1.0，当前为{total}")
        return v

    def get_retrieval_mode(self) -> str:
        """获取检索模式"""
        if self.bm25_weight == 0.0:
            return "vector_only"
        elif self.vector_weight == 0.0:
            return "bm25_only"
        else:
            return "hybrid"

    def get_weights_dict(self) -> dict:
        """获取权重字典"""
        return {
            "bm25_weight": self.bm25_weight,
            "vector_weight": self.vector_weight
        }
