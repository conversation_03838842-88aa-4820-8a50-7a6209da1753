# CORS 跨域解决方案完整指南

## 📖 概述

本文档详细记录了在 FastAPI + Vue.js 项目中解决跨域资源共享（CORS）问题的完整经验，包括问题分析、解决方案、代码实现和最佳实践。

## 🚨 问题背景

### 典型错误现象

当从外部域名（如 `http://127.0.0.1:5500`）访问 FastAPI 服务（`http://localhost:8000`）时，浏览器会报错：

```javascript
AxiosError: Network Error
ERR_NETWORK
```

### 根本原因

浏览器的**同源策略**阻止了跨域请求。同源策略要求协议、域名、端口三者完全相同才能正常访问。

**不同源的例子：**

- `http://127.0.0.1:5500` → `http://localhost:8000` （域名不同）
- `http://localhost:5500` → `http://localhost:8000` （端口不同）
- `https://example.com` → `http://example.com` （协议不同）

## 🔧 解决方案架构

### 1. 服务端配置（FastAPI）

#### 1.1 CORS 域名白名单配置

**文件：** `backend/config/settings.py`

```python
from typing import List
from pydantic import BaseSettings

class Settings(BaseSettings):
    # CORS配置
    allowed_origins: List[str] = [
        "http://localhost:3000",      # React开发服务器
        "http://127.0.0.1:3000",
        "http://localhost:8000",      # FastAPI自身
        "http://127.0.0.1:8000",
        "http://localhost:5500",      # Live Server默认端口
        "http://127.0.0.1:5500",      # Live Server默认端口
        "http://localhost:5501",      # Live Server备用端口
        "http://127.0.0.1:5501",
        "file://",                    # 本地文件访问
        "*"                           # 开发环境：允许所有域名
    ]

settings = Settings()
```

**配置说明：**

- 包含常用的开发端口
- `file://` 支持本地 HTML 文件访问
- `*` 通配符适用于开发环境

#### 1.2 CORS 中间件配置

**文件：** `backend/app/core/middleware.py`

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from backend.config.settings import settings

def add_middlewares(app: FastAPI) -> None:
    """添加CORS中间件"""

    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],                    # 开发环境允许所有来源
        allow_credentials=False,                # 使用*时必须为False
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],                    # 允许所有请求头
        expose_headers=["*"],                   # 暴露所有响应头
        max_age=600,                           # 预检请求缓存时间
    )
```

**关键参数解释：**

| 参数                | 说明             | 注意事项                                      |
| ------------------- | ---------------- | --------------------------------------------- |
| `allow_origins`     | 允许的源域名列表 | 使用`["*"]`时不能设置`allow_credentials=True` |
| `allow_credentials` | 是否允许携带凭证 | 包括 cookies、authorization headers 等        |
| `allow_methods`     | 允许的 HTTP 方法 | 必须包含`OPTIONS`用于预检请求                 |
| `allow_headers`     | 允许的请求头     | `["*"]`允许所有头部                           |
| `max_age`           | 预检请求缓存时间 | 减少重复的 OPTIONS 请求                       |

### 2. 前端配置（JavaScript）

#### 2.1 API 请求配置

**文件：** `ai_chat_script.js`

```javascript
// API配置
const CONFIG = {
  API_BASE_URL: "http://localhost:8000/api/v1", // 完整API地址
  MAX_RESULTS: 3,
  SIMILARITY_THRESHOLD: 0.7,
};

// 带超时控制的请求函数
async function makeRequest(url, options = {}) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 30000);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    });

    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

// 检查连接状态
async function checkConnection() {
  try {
    const response = await makeRequest(`${CONFIG.API_BASE_URL}/status`);

    if (response.ok) {
      const data = await response.json();
      console.log("✅ API连接成功:", data);
      return true;
    }
  } catch (error) {
    if (error.name === "AbortError") {
      console.error("❌ 请求超时");
    } else {
      console.error("❌ 连接失败:", error);
    }
    return false;
  }
}

// 发送查询请求
async function sendQuery(message) {
  try {
    const response = await makeRequest(`${CONFIG.API_BASE_URL}/query`, {
      method: "POST",
      body: JSON.stringify({
        query: message,
        max_results: CONFIG.MAX_RESULTS,
        similarity_threshold: CONFIG.SIMILARITY_THRESHOLD,
      }),
    });

    if (response.ok) {
      return await response.json();
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error("查询失败:", error);
    throw error;
  }
}
```

#### 2.2 错误处理最佳实践

```javascript
// 统一的错误处理函数
function handleRequestError(error) {
  let errorMessage = "请求失败：";

  if (error.name === "AbortError") {
    errorMessage += "请求超时，请稍后重试";
  } else if (error.message.includes("HTTP")) {
    errorMessage += `服务器错误 ${error.message}`;
  } else if (error.name === "TypeError") {
    errorMessage +=
      "无法连接到服务器，请检查：\n1. 服务是否启动\n2. 网络连接\n3. CORS配置";
  } else {
    errorMessage += error.message;
  }

  return errorMessage;
}
```

## 🔍 CORS 工作原理

### 预检请求流程

1. **浏览器发送 OPTIONS 预检请求**

   ```http
   OPTIONS /api/v1/query HTTP/1.1
   Host: localhost:8000
   Origin: http://127.0.0.1:5500
   Access-Control-Request-Method: POST
   Access-Control-Request-Headers: Content-Type
   ```

2. **服务器响应预检请求**

   ```http
   HTTP/1.1 200 OK
   Access-Control-Allow-Origin: *
   Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
   Access-Control-Allow-Headers: Content-Type
   Access-Control-Max-Age: 600
   ```

3. **浏览器发送实际请求**

   ```http
   POST /api/v1/query HTTP/1.1
   Host: localhost:8000
   Origin: http://127.0.0.1:5500
   Content-Type: application/json

   {"query": "你好", "max_results": 3}
   ```

### 简单请求 vs 复杂请求

**简单请求（无需预检）：**

- 方法：GET、HEAD、POST
- 头部：Accept、Accept-Language、Content-Language、Content-Type（限定值）
- Content-Type：text/plain、multipart/form-data、application/x-www-form-urlencoded

**复杂请求（需要预检）：**

- 使用 PUT、DELETE 等方法
- 包含自定义头部
- Content-Type 为 application/json

## 🧪 测试和验证

### 1. 命令行测试

```bash
# 测试预检请求
curl -H "Origin: http://127.0.0.1:5500" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     http://localhost:8000/api/v1/status -v

# 测试实际请求
curl -H "Origin: http://127.0.0.1:5500" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{"query":"测试","max_results":3,"similarity_threshold":0.7}' \
     http://localhost:8000/api/v1/query
```

### 2. 自动化测试脚本

**文件：** `scripts/test_cors.py`

```python
import requests

def test_cors_configuration():
    """测试CORS配置"""
    base_url = "http://localhost:8000/api/v1"
    origin = "http://127.0.0.1:5500"

    # 测试预检请求
    options_response = requests.options(
        f"{base_url}/status",
        headers={
            'Origin': origin,
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
    )

    print(f"预检请求状态: {options_response.status_code}")
    print(f"允许的源: {options_response.headers.get('Access-Control-Allow-Origin')}")
    print(f"允许的方法: {options_response.headers.get('Access-Control-Allow-Methods')}")

    # 测试实际请求
    get_response = requests.get(
        f"{base_url}/status",
        headers={'Origin': origin}
    )

    print(f"实际请求状态: {get_response.status_code}")
    return options_response.status_code == 200 and get_response.status_code == 200

if __name__ == "__main__":
    if test_cors_configuration():
        print("✅ CORS配置正确")
    else:
        print("❌ CORS配置有问题")
```

## 🚀 部署和生产环境

### 生产环境 CORS 配置

```python
# 生产环境配置示例
class ProductionSettings(Settings):
    allowed_origins: List[str] = [
        "https://yourdomain.com",
        "https://www.yourdomain.com",
        "https://app.yourdomain.com"
    ]

    def configure_cors(self, app: FastAPI):
        app.add_middleware(
            CORSMiddleware,
            allow_origins=self.allowed_origins,     # 明确指定域名
            allow_credentials=True,                 # 可以启用凭证
            allow_methods=["GET", "POST"],          # 限制方法
            allow_headers=["Content-Type", "Authorization"],  # 限制头部
            max_age=3600,                          # 更长的缓存时间
        )
```

### 环境变量配置

```bash
# .env.production
CORS_ALLOWED_ORIGINS=["https://yourdomain.com","https://www.yourdomain.com"]
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=3600
```

## 🔒 安全最佳实践

### 1. 避免使用通配符

```python
# ❌ 不安全 - 生产环境避免使用
allow_origins=["*"]

# ✅ 安全 - 明确指定域名
allow_origins=[
    "https://yourdomain.com",
    "https://app.yourdomain.com"
]
```

### 2. 限制 HTTP 方法

```python
# ❌ 过于宽松
allow_methods=["*"]

# ✅ 按需限制
allow_methods=["GET", "POST", "PUT", "DELETE"]
```

### 3. 谨慎处理凭证

```python
# 当需要发送cookies或认证头时
allow_credentials=True
# 此时不能使用 allow_origins=["*"]
```

## 🛠️ 故障排除

### 常见问题和解决方案

| 问题          | 原因                  | 解决方案                            |
| ------------- | --------------------- | ----------------------------------- |
| Network Error | CORS 配置错误         | 检查服务端 CORS 设置                |
| 预检请求失败  | 缺少 OPTIONS 方法支持 | 确保`allow_methods`包含 OPTIONS     |
| 凭证发送失败  | credentials 配置冲突  | 不能同时使用`*`和`credentials=True` |
| 请求头被拒绝  | 自定义头部未允许      | 添加到`allow_headers`列表           |

### 调试技巧

1. **浏览器开发者工具**

   - Network 标签查看请求详情
   - Console 查看 CORS 错误信息

2. **服务器日志**

   ```python
   # 添加CORS调试日志
   import logging
   logging.getLogger("fastapi.middleware.cors").setLevel(logging.DEBUG)
   ```

3. **请求头检查**
   ```javascript
   // 检查响应头
   fetch(url).then((response) => {
     console.log("CORS headers:", {
       "Access-Control-Allow-Origin": response.headers.get(
         "Access-Control-Allow-Origin"
       ),
       "Access-Control-Allow-Methods": response.headers.get(
         "Access-Control-Allow-Methods"
       ),
     });
   });
   ```

## 📚 参考资源

- [MDN CORS 文档](https://developer.mozilla.org/zh-CN/docs/Web/HTTP/CORS)
- [FastAPI CORS 文档](https://fastapi.tiangolo.com/tutorial/cors/)
- [W3C CORS 规范](https://www.w3.org/TR/cors/)

## 🎯 实际案例分析

### 本项目的具体实现

在我们的 FastAPI + AI 聊天项目中，遇到的具体场景：

**问题场景：**

- 用户通过 Live Server (`http://127.0.0.1:5500`) 打开 `ai_chat.html`
- 页面需要访问 FastAPI 服务 (`http://localhost:8000/api/v1`)
- 浏览器阻止跨域请求，显示 `Network Error`

**解决过程：**

1. **识别问题**

   ```javascript
   // 错误信息
   AxiosError: Network Error
   ERR_NETWORK
   ```

2. **配置服务端**

   ```python
   # backend/app/core/middleware.py
   app.add_middleware(
       CORSMiddleware,
       allow_origins=["*"],  # 关键：允许所有源
       allow_credentials=False,
       allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
       allow_headers=["*"],
   )
   ```

3. **修改前端请求**

   ```javascript
   // 使用完整API地址
   const API_BASE_URL = "http://localhost:8000/api/v1";

   // 正确的请求方式
   const response = await fetch(`${API_BASE_URL}/query`, {
     method: "POST",
     headers: {
       "Content-Type": "application/json",
     },
     body: JSON.stringify(data),
   });
   ```

4. **验证结果**

   ```bash
   # 使用curl验证CORS配置
   curl -H "Origin: http://127.0.0.1:5500" \
        -X OPTIONS \
        http://localhost:8000/api/v1/status -v

   # 响应包含正确的CORS头部
   Access-Control-Allow-Origin: *
   Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
   ```

## 🔄 快速重启和测试工具

### 自动重启脚本

**文件：** `scripts/restart_with_cors.py`

```python
#!/usr/bin/env python3
"""
重启FastAPI服务并测试CORS配置
"""
import subprocess
import time
import requests

def restart_fastapi():
    """重启FastAPI服务"""
    # 终止现有进程
    subprocess.run(['pkill', '-f', 'uvicorn'], check=False)
    time.sleep(2)

    # 启动新进程
    subprocess.Popen([
        'python', '-m', 'uvicorn',
        'backend.app.main:app',
        '--host', '0.0.0.0',
        '--port', '8000',
        '--reload'
    ])

    print("✅ FastAPI服务已重启")

def test_cors():
    """测试CORS配置"""
    time.sleep(5)  # 等待服务启动

    try:
        # 测试预检请求
        response = requests.options(
            'http://localhost:8000/api/v1/status',
            headers={'Origin': 'http://127.0.0.1:5500'}
        )

        if response.status_code == 200:
            print("✅ CORS预检请求成功")
            print(f"允许的源: {response.headers.get('Access-Control-Allow-Origin')}")
            return True
        else:
            print(f"❌ CORS预检请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ CORS测试失败: {e}")
        return False

if __name__ == "__main__":
    restart_fastapi()
    test_cors()
```

## 📋 检查清单

### 部署前 CORS 检查清单

- [ ] **服务端配置**

  - [ ] CORSMiddleware 已正确添加
  - [ ] allow_origins 包含所需域名
  - [ ] allow_methods 包含 OPTIONS
  - [ ] allow_headers 配置正确

- [ ] **前端配置**

  - [ ] 使用完整 API 地址
  - [ ] 设置正确的 Content-Type
  - [ ] 错误处理包含 CORS 相关错误

- [ ] **测试验证**

  - [ ] 预检请求返回 200
  - [ ] 实际请求成功
  - [ ] 浏览器控制台无 CORS 错误

- [ ] **安全检查**
  - [ ] 生产环境不使用通配符
  - [ ] 凭证配置合理
  - [ ] 只允许必要的 HTTP 方法

## 🚨 常见陷阱

### 1. 凭证和通配符冲突

```python
# ❌ 错误配置 - 会导致CORS失败
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,  # 与通配符冲突
)

# ✅ 正确配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,  # 使用通配符时必须为False
)
```

### 2. 忘记包含 OPTIONS 方法

```python
# ❌ 缺少OPTIONS - 预检请求失败
allow_methods=["GET", "POST"]

# ✅ 包含OPTIONS
allow_methods=["GET", "POST", "OPTIONS"]
```

### 3. 端口号不匹配

```javascript
// ❌ 端口不匹配
const API_BASE_URL = "http://localhost:3000/api/v1"; // 错误端口

// ✅ 正确端口
const API_BASE_URL = "http://localhost:8000/api/v1"; // FastAPI默认端口
```

## 📝 总结

通过合理配置服务端 CORS 中间件和前端请求方式，可以有效解决跨域问题。关键要点：

1. **服务端**：正确配置 CORSMiddleware，注意凭证和通配符的关系
2. **前端**：使用完整 API 地址和正确的请求头
3. **测试**：验证预检请求和实际请求都能成功
4. **安全**：生产环境避免使用通配符配置
5. **调试**：使用浏览器开发者工具和命令行工具验证

这套解决方案已在本项目中验证有效，从 `Network Error` 到成功的 API 调用，可以作为其他类似项目的参考模板。

---

**文档版本：** v1.0
**最后更新：** 2025-07-05
**适用项目：** FastAPI + Vue.js/原生 JavaScript
**测试环境：** Windows 10, Chrome 浏览器, Live Server
