# Qdrant 向量数据库部署指南

## 概述

本文档描述如何为RAG系统部署和配置Qdrant向量数据库。

## 部署方式

### 方式1：Docker Compose（推荐）

使用项目根目录的 `docker-compose.qdrant.yml` 文件：

```bash
# 启动Qdrant服务
docker-compose -f docker-compose.qdrant.yml up -d

# 查看服务状态
docker-compose -f docker-compose.qdrant.yml ps

# 查看日志
docker-compose -f docker-compose.qdrant.yml logs -f qdrant

# 停止服务
docker-compose -f docker-compose.qdrant.yml down
```

### 方式2：直接Docker运行

```bash
# 创建数据目录
mkdir -p qdrant_data

# 运行Qdrant容器
docker run -d \
  --name qdrant \
  -p 6333:6333 \
  -p 6334:6334 \
  -v $(pwd)/qdrant_data:/qdrant/storage \
  qdrant/qdrant:latest
```

## 验证部署

### 1. 检查服务状态

```bash
# 访问健康检查端点
curl http://localhost:6333/

# 预期响应
{"title":"qdrant - vector search engine","version":"1.14.1","commit":"..."}
```

### 2. 检查集合列表

```bash
curl http://localhost:6333/collections

# 预期响应（新安装为空）
{"result":{"collections":[]},"status":"ok","time":0.000074219}
```

## 配置说明

### 端口配置

- **6333**: HTTP API端口（主要用于REST API调用）
- **6334**: gRPC端口（高性能通信，LlamaIndex推荐）

### 数据持久化

- 数据存储在 `./qdrant_data` 目录
- 容器重启后数据保持不变
- 建议定期备份此目录

### 性能优化

Qdrant默认配置已针对大多数用例优化，如需调整：

1. 内存使用：通过环境变量 `QDRANT__STORAGE__MEMORY_THRESHOLD_MB`
2. 并发设置：通过 `QDRANT__SERVICE__MAX_REQUEST_SIZE_MB`
3. 索引参数：在创建集合时指定HNSW参数

## 安全配置

### API密钥（可选）

在生产环境中，建议启用API密钥认证：

```yaml
environment:
  - QDRANT__SERVICE__API_KEY=your_secure_api_key_here
```

对应的应用配置：

```env
QDRANT_API_KEY=your_secure_api_key_here
```

## 故障排除

### 常见问题

1. **端口冲突**：确保6333和6334端口未被占用
2. **权限问题**：确保Docker有权限访问数据目录
3. **内存不足**：Qdrant需要足够内存进行向量索引

### 日志查看

```bash
# Docker Compose方式
docker-compose -f docker-compose.qdrant.yml logs -f qdrant

# 直接Docker方式
docker logs -f qdrant
```

## 迁移注意事项

从ChromaDB迁移到Qdrant时：

1. **数据不兼容**：需要重新索引所有文档
2. **配置更新**：更新应用配置文件
3. **依赖更新**：安装Qdrant相关Python包
4. **测试验证**：确保所有功能正常工作

## 监控和维护

### 健康检查

Qdrant提供内置健康检查端点，Docker Compose配置已包含自动健康检查。

### 备份策略

```bash
# 停止服务
docker-compose -f docker-compose.qdrant.yml stop

# 备份数据
tar -czf qdrant_backup_$(date +%Y%m%d_%H%M%S).tar.gz qdrant_data/

# 重启服务
docker-compose -f docker-compose.qdrant.yml start
```

### 更新Qdrant

```bash
# 拉取最新镜像
docker-compose -f docker-compose.qdrant.yml pull

# 重启服务
docker-compose -f docker-compose.qdrant.yml up -d
```
